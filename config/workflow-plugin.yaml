---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nile-workflow
  namespace: helm-yh-dev
data:
  workflow.yaml: |-
    syslog:
    - regexp:
        patterns:
        # RFC5424 with ISO8601 timestamp
        - '^(?:<(?P<pri>\d{1,3})>\d{1,3} )?(?P<time>(?:\d\d){1,2}-(?:0?[1-9]|1[0-2])-(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?) (?P<host>[!-~]{1,255}) (?P<pid>[!-~]{1,128}) (?P<msgid>[!-~]{1,32}) (?P<extradata>(?:\-|(?:\[.*?\])+)) *(?s:(?P<message>.+))?$'
        # RFC3164 with SYSLOGTIMESTAMP
        - '^(?:<(?P<pri>[0-9]{1,3})> )?(?P<time>\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\b +(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9]) (?:[^0-9]?)(?:2[0123]|[01]?[0-9]):(?:[0-5][0-9])(?::(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))(?:[^0-9]?)) (?P<host>[^ ]*) *(?s:(?P<message>.*))$'
        # RFC3164 with SYSLOGTIMESTAMP but no host and process name
        - '^(?:<(?P<pri>[0-9]{1,3})> )?(?P<time>\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\b +(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9]) (?:[^0-9]?)(?:2[0123]|[01]?[0-9]):(?:[0-5][0-9])(?::(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))(?:[^0-9]?)) *(?s:(?P<message>.*))$'
        # RFC3164 with ISO8601 timestamp
        - '^(?:<(?P<pri>[0-9]{1,3})> )?(?P<time>(?:\d\d){1,2}-(?:0?[1-9]|1[0-2])-(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?) (?P<host>[^ ]*) *(?s:(?P<message>.*))$'
    - date:
        field: time
