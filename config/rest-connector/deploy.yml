apiVersion: apps/v1
kind: Deployment
metadata:
  name: rest-connector
  labels:
    env: rest-connector
spec:
  selector:
    matchLabels:
      env: rest-connector
  template:
    metadata:
      name: rest-connector
      labels:
        env: rest-connector
    spec:
      containers:
      - image: nexus.yanhuangdata.com:5000/yanhuang/nile:develop.2.0.0.20210625-143235.c1778cd4
        name: rest-connector
        command: ["rest-connector"]
        args: ["--stonewave", "stonewave:9972", "--brokers", "kafka-dev.kafka.svc.cluster.local:9092", "--consumers", "3", "--prefix", "_gdi_", "--verbose"]
        ports:
        - containerPort: 9090
      nodeSelector:
        yanhuangdata.com/dev: node4