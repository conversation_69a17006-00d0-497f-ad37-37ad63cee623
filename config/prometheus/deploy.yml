apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  labels:
    env: metrics
spec:
  selector:
    matchLabels:
      env: metrics
  template:
    metadata:
      name: prometheus
      labels:
        env: metrics
    spec:
      containers:
      - image: prom/prometheus
        name: prom
        args:
        - '--config.file=/prometheus.yml'
        - --storage.tsdb.path='prometheus_data/
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-persistent-storage-store
          mountPath: prometheus_data/
        - name: prometheus-script
          mountPath: /prometheus.yml
          subPath: prometheus.yml  
      volumes:
      - name: prometheus-persistent-storage-store
        persistentVolumeClaim:
          claimName: prom-storage-pvc
      - name: prometheus-script
        configMap:
          name: prom-config