/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package restconnector

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"nile/pkg/stonewave"
	"nile/pkg/transform"
	"os"
	"strings"
	"sync/atomic"
	"unicode/utf8"

	"go.uber.org/zap"
)

type GDIOption struct {
	datatype  string
	metadata  map[string][]string
	r         io.Reader
	cleanup   func()
	maxEvents int
	hei       bool
}

// GDIService provides data ingestion
type GDIService interface {
	Ingest(context.Context, *GDIOption) (<-chan *transform.Event, error)
}

type gdiService struct {
	dm *stonewave.DatatypeMap
}

func NewGDIService(dm *stonewave.DatatypeMap, workflowConcurrency int) (GDIService, error) {
	return &gdiService{
		dm: dm,
	}, nil
}

func (s *gdiService) Ingest(ctx context.Context, o *GDIOption) (<-chan *transform.Event, error) {
	datatype := s.dm.GetDatatype(o.datatype)
	// overwrite datatype settings by url parameters (metadata)
	stonewave.DatatypeDecoder.Decode(&datatype, o.metadata)

	var emitter transform.Emitter
	var workflow *transform.Workflow
	var err error
	if o.hei {
		if emitter, err = transform.NewHEIEmitter(o.r, nil); err != nil {
			return nil, fmt.Errorf("fail in creating HEI emitter: %w", err)
		}
		workflow = transform.NewWorkflowForDatatype(&datatype)
	} else {
		if datatype.IngestionTimeExtraction == "json" {
			if _, _is_array := o.metadata["_is_json_array"]; _is_array {
				preprocessJson(o)
			}
		}

		if emitter, err = transform.NewEmitterForDatatype(&datatype, o.r); err != nil {
			return nil, fmt.Errorf("fail in creating emitter: %w", err)
		}

		// try to set fields from csv first line header
		if datatype.IngestionTimeExtraction == "csv" && len(datatype.IngestionTimeFieldNames) == 0 {
			d, _ := utf8.DecodeRuneInString(datatype.Delimiter)
			if d == utf8.RuneError {
				d = ','
			}

			fields := parseCSVFields(o.r, d)
			if len(fields) > 0 {
				datatype.IngestionTimeFieldNames = fields
			}
		}

		workflow = transform.NewFileWorkflowForDatatype(&datatype)
	}
	workflow.Start()

	var count int64

	output := make(chan *transform.Event)
	go func() {
		defer close(output)
		for event := range workflow.Output() {
			// event is nil when filtered out during processing
			if event != nil {
				atomic.AddInt64(&count, 1)
				if o.maxEvents == 0 || atomic.LoadInt64(&count) <= int64(o.maxEvents) {
					output <- event
				}
			}
		}
	}()

	go func() {
		defer func() {
			if o.cleanup != nil {
				o.cleanup()
			}
			workflow.AsyncClose()
		}()

		// count := 0
		for {
			if o.maxEvents != 0 && atomic.LoadInt64(&count) >= int64(o.maxEvents) {
				break
			}
			event, err := emitter.Emit()
			if err == io.EOF {
				break
			}

			if err != nil {
				zap.S().Errorw("fail in emitting event", "error", err)
				continue
			}

			select {
			case workflow.Input() <- addMetadata(event, o):
				// count += 1
			case <-ctx.Done():
				return
			}
		}
	}()

	return output, nil
}

func addMetadata(event *transform.Event, o *GDIOption) *transform.Event {
	event.Datatype = o.datatype

	if es, ok := o.metadata["event_set"]; ok && len(es) > 0 && len(es[0]) > 0 {
		event.EventSet = es[0]
	}

	if s, ok := o.metadata["source"]; ok && len(s) > 0 && len(s[0]) > 0 {
		event.SetField("_source", s[0])
	}

	if s, ok := o.metadata["host"]; ok && len(s) > 0 && len(s[0]) > 0 {
		event.SetField("_host", s[0])
	}

	return event
}

func parseCSVFields(r io.Reader, comma rune) []string {
	cr := csv.NewReader(r)
	cr.Comma = comma
	if comma != ' ' && comma != '\t' {
		cr.TrimLeadingSpace = true
	}

	for {
		records, err := cr.Read()
		if err != nil && err != csv.ErrFieldCount {
			return nil
		}

		if len(records) > 0 && strings.HasPrefix(strings.TrimSpace(records[0]), "#") {
			if len(records) == 1 {
				cr.FieldsPerRecord = 0
				continue
			}

			records[0] = records[0][1:]
		}

		for i := range records {
			records[i] = strings.TrimSpace(records[i])
		}

		zap.S().Debugw("parsed csv field names", "fields", records)
		return records
	}

}

func preprocessJson(o *GDIOption) {
	// json := jsoniter.ConfigCompatibleWithStandardLibrary
	file1, err := ioutil.TempFile("", "json-preprocess")
	if err != nil {
		return
	}

	file2, err := ioutil.TempFile("", "json-processed")
	if err != nil {
		return
	}

	io.Copy(file1, o.r)
	file1.Seek(0, io.SeekStart)

	defer func() {
		if file2 != nil {
			file2.Close()
		}

		file1.Seek(0, io.SeekStart)
		o.r = file1
		cleanup := o.cleanup
		o.cleanup = func() {
			file1.Close()
			os.Remove(file1.Name())
			cleanup()
		}
	}()

	dec := json.NewDecoder(file1)
	if t, _ := dec.Token(); t != json.Delim('[') {
		fmt.Println(t)
		return
	}

	for dec.More() {
		var obj map[string]interface{}
		if err := dec.Decode(&obj); err != nil {
			return
		}

		data, _ := json.Marshal(obj)
		file2.Write(data)
		file2.Write([]byte{'\n'})
	}

	file1.Close()
	os.Remove(file1.Name())
	file1 = file2
	file2 = nil
}
