/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package restconnector

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	_ "net/http/pprof"
	"nile/pkg/kafka"
	"nile/pkg/transform"
	"strings"
	"sync/atomic"
	"time"
	"unicode/utf8"

	confluent "github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/spf13/viper"

	"github.com/gogs/chardet"
	jsoniter "github.com/json-iterator/go"
	"github.com/segmentio/ksuid"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"

	"github.com/go-kit/kit/endpoint"
	"github.com/go-kit/kit/transport"
	httptransport "github.com/go-kit/kit/transport/http"
	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

const maxMemory = 32 << 20

var jsonAPI = jsoniter.Config{
	EscapeHTML: true,
	UseNumber:  true,
}.Froze()

// MakeHTTPHandler mounts all of the service endpoints into an http.Handler.
// Useful in a profilesvc server.
func MakeHTTPHandler(svc GDIService, brokers string) http.Handler {
	r := mux.NewRouter()
	options := []httptransport.ServerOption{
		httptransport.ServerErrorHandler(transport.ErrorHandlerFunc(logError)),
		httptransport.ServerErrorEncoder(encodeError),
	}

	if strings.HasPrefix(brokers, "http://") || strings.HasPrefix(brokers, "https://") {
		r.Methods("POST").Path("/{sourcetype}/datatype/{datatype}/ingestion").Handler(
			httptransport.NewServer(
				makeIngestEndpoint(svc),
				decodeGDIRequest,
				makeVectorIngestResponseEncoder(brokers),
				options...,
			),
		)
	} else {
		r.Methods("POST").Path("/{sourcetype}/datatype/{datatype}/ingestion").Handler(
			httptransport.NewServer(
				makeIngestEndpoint(svc),
				decodeGDIRequest,
				makeIngestResponseEncoder(brokers),
				options...,
			),
		)
	}

	r.Methods("POST").Path("/{sourcetype}/datatype/{datatype}/preview").Handler(
		httptransport.NewServer(
			makeIngestEndpoint(svc),
			decodePreviewRequest,
			encodePreviewResponse,
			options...,
		),
	)

	// register prometheus
	r.Methods("GET").Path("/metrics").Handler(
		promhttp.Handler(),
	)

	// for /debug/pprof
	r.PathPrefix("/debug").Handler(http.DefaultServeMux)

	return r
}

type ingestResponse struct {
	Output <-chan *transform.Event
}

func makeIngestEndpoint(svc GDIService) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		o := request.(*GDIOption)
		result, err := svc.Ingest(ctx, o)

		return ingestResponse{Output: result}, err
	}
}

const (
	datatypeKey = "datatype"
	fileKey     = "file"
)

func decodeGDIRequest(_ context.Context, r *http.Request) (interface{}, error) {
	zap.S().Infow("request", "url", r.URL)

	var request GDIOption

	var rd io.ReadCloser
	if strings.Contains(r.Header.Get("Content-Type"), "multipart/form-data") {
		// Parse parameters
		err := r.ParseMultipartForm(maxMemory)
		if err != nil {
			return nil, fmt.Errorf("parse multipart form: %w", err)
		}

		request.cleanup = func() {
			io.Copy(ioutil.Discard, rd)
			rd.Close()
			r.MultipartForm.RemoveAll()
		}

		file, _, err := r.FormFile(fileKey)
		if err != nil {
			return nil, fmt.Errorf("fail in open file: %w", err)
		}
		rd = file
	} else {
		r.ParseForm()
		rd = r.Body

		request.cleanup = func() {
			io.Copy(ioutil.Discard, rd)
			rd.Close()
		}
	}

	vars := mux.Vars(r)
	sourceType := vars["sourcetype"]
	dataType := vars["datatype"]

	request.hei = sourceType == "hei"
	request.datatype = dataType
	request.metadata = r.Form

	// remove BOM if exists
	br := bufio.NewReader(rd)
	fr, _, err := br.ReadRune()
	if err == nil && fr != '\uFEFF' {
		br.UnreadRune() // Not a BOM -- put the rune back
	}
	request.r = br

	// try to decode non UTF-8 content
	data, err := br.Peek(1024)
	if err != nil && err != io.EOF {
		return nil, fmt.Errorf("peek data: %w", err)
	}

	dt := chardet.NewTextDetector()
	res, err := dt.DetectBest(data)
	detected := false
	if err == nil && res.Confidence == 100 {
		zap.S().Infow("detected content encoding", "charset", res.Charset)
		switch res.Charset {
		case "GB18030":
			request.r = simplifiedchinese.GB18030.NewDecoder().Reader(br)
			detected = true
		case "Big5":
			request.r = traditionalchinese.Big5.NewDecoder().Reader(br)
			detected = true
		}
	}

	if !detected {
		strict := r.Form["strict"]
		if len(strict) > 0 && strict[0] == "true" && !isUTF8(data) {
			return nil, errors.New("content is not utf-8 encoded")
		}
	}

	if td := bytes.TrimSpace(data); len(td) > 0 && td[0] == '[' {
		request.metadata["_is_json_array"] = []string{"true"}
	}

	return &request, nil
}

func isUTF8(data []byte) bool {
	for len(data) > 4 {
		r, size := utf8.DecodeRune(data)
		if r == utf8.RuneError {
			return false
		}
		data = data[size:]
	}
	return true
}

func decodePreviewRequest(ctx context.Context, r *http.Request) (interface{}, error) {
	req, err := decodeGDIRequest(ctx, r)

	if err == nil {
		// preview handle at most 1000 events
		req.(*GDIOption).maxEvents = 1000
	}
	return req, err
}

func encodePreviewResponse(_ context.Context, w http.ResponseWriter, response interface{}) error {
	resp := response.(ingestResponse)

	// use buffer to make sure request body is consumed and closed before writing response
	var buf bytes.Buffer
	line := 0
	for event := range resp.Output {
		if buf.Len() > 0 {
			buf.WriteByte('\n')
		}
		buf.Write(event.ToPreviewBytes())
		line += 1
	}

	w.Header().Set("Content-Type", "text/plain; charset=utf8")
	w.WriteHeader(http.StatusOK)
	w.Write(buf.Bytes())

	zap.S().Infow("preview transport done", "count", line, "size", buf.Len())

	return nil
}

func makeIngestResponseEncoder(brokers string) func(ctx context.Context, w http.ResponseWriter, response interface{}) error {
	return func(ctx context.Context, w http.ResponseWriter, response interface{}) error {
		defer func() {
			if err := recover(); err != nil {
				zap.S().Infof("recover panic: %s", err)
			}
		}()

		start := time.Now()

		resp := response.(ingestResponse)

		var iid string
		ksuid, err := ksuid.NewRandom()
		if err == nil {
			iid = ksuid.String()
		}

		type responseType struct {
			ID     string   `json:"id"`
			Count  int      `json:"count"`
			Errors []string `json:"errors,omitempty"`
		}

		res := responseType{ID: iid}
		defer func() {
			zap.S().Infow("transport done", "id", iid, "response", res, "duration", time.Since(start))
		}()

		config := confluent.ConfigMap{
			"bootstrap.servers":          brokers,
			"allow.auto.create.topics":   false,
			"enable.idempotence":         true,
			"compression.type":           "lz4",
			"linger.ms":                  100,
			"queue.buffering.max.kbytes": viper.GetInt("queue-buffering-max-kbytes"),
			"go.delivery.reports":        true,
			"go.produce.channel.size":    1000,
			"message.max.bytes":          viper.GetInt("max-message-bytes"),
		}

		producer, err := confluent.NewProducer(&config)
		if err != nil {
			return err
		}
		defer func() {
			producer.Flush(60000)
			producer.Close()
		}()

		terminate := make(chan struct{})

		var counter int64 = 1

		// send events
		go func() {
			defer func() {
				// goroutinue may panic after producer is closed
				if err := recover(); err != nil {
					zap.S().Infof("recover panic: %s", err)
				}

				if atomic.AddInt64(&counter, -1) == 0 {
					// force quit when no events sent. will cause panic in close producer
					close(producer.Events())
				}

				for range resp.Output {
				}
			}()

			for {
				select {
				case event, chanOpen := <-resp.Output:
					if !chanOpen {
						return
					}

					msg := kafka.GenerateMessageFromEvent(event)
					if msg == nil {
						zap.S().Infof("skip sending empty event")
						continue
					}
					msg.Key = []byte(iid)

					atomic.AddInt64(&counter, 1)
					producer.ProduceChannel() <- msg
				case <-terminate:
					return
				}
			}
		}()

		// handle producer returns
		go func() {
			defer func() {
				if err := recover(); err != nil {
					zap.S().Infof("recover panic: %s", err)
				}
				close(terminate)
			}()

			for e := range producer.Events() {
				switch ev := e.(type) {
				// Message delivery report
				case *confluent.Message:
					if ev.TopicPartition.Error != nil {
						res.Errors = append(res.Errors, fmt.Sprintf("delivery failure: %s", ev.TopicPartition.Error))
					} else {
						res.Count++
					}

					if atomic.AddInt64(&counter, -1) == 0 {
						return
					}

				case confluent.Error:
					if ev.IsFatal() {
						res.Errors = append(res.Errors, fmt.Sprintf("fatal error: %s", ev))
						return
					}

				default:
					zap.S().Infow("ignored producer event", "event", ev)
				}

			}
		}()

		select {
		case <-terminate:
		case <-ctx.Done():
			return ctx.Err()
		}

		res.Errors = getFirstErrors(res.Errors)
		if data, err := jsoniter.Marshal(res); err == nil {
			w.Header().Set("Content-Type", "application/json")
			if len(res.Errors) > 0 {
				w.WriteHeader(http.StatusInternalServerError)
			} else {
				w.WriteHeader(http.StatusOK)
			}
			w.Write(data)
		} else {
			encodeError(ctx, errors.New("error in encoding response"), w)
		}
		return nil
	}
}

func makeVectorIngestResponseEncoder(url string) func(ctx context.Context, w http.ResponseWriter, response interface{}) error {
	return func(ctx context.Context, w http.ResponseWriter, response interface{}) error {
		defer func() {
			if err := recover(); err != nil {
				zap.S().Infof("recover panic: %s", err)
			}
		}()

		start := time.Now()

		resp := response.(ingestResponse)

		var iid string
		ksuid, err := ksuid.NewRandom()
		if err == nil {
			iid = ksuid.String()
		}

		type responseType struct {
			ID     string   `json:"id"`
			Count  int      `json:"count"`
			Errors []string `json:"errors,omitempty"`
		}

		res := responseType{ID: iid}
		defer func() {
			zap.S().Infow("transport done", "id", iid, "response", res, "duration", time.Since(start))
		}()

		terminate := make(chan struct{})

		// send events
		go func() {
			defer func() {
				if err := recover(); err != nil {
					zap.S().Infof("recover panic: %s", err)
				}
				close(terminate)
			}()

			var eventSet string

			client := http.DefaultClient
			maxBufferSize := viper.GetInt("max-message-bytes") * 4 / 5
			batchSize := 10000
			var buffer []map[string]interface{}
			messageSize := 0
			// buffer["sw_events"] = make([]map[string]interface{}, 0, 100)

			flushBuffer := func() int {
				batchCnt := len(buffer)

				data, err := jsonAPI.Marshal(buffer)
				buffer = buffer[:0]
				messageSize = 0
				if err != nil {
					zap.S().Infow("fail to generate sw_events", "error", err)
					return 0
				}

				rd := bytes.NewReader(data)
				req, err := http.NewRequest("POST", url, rd)
				if err != nil {
					res.Errors = append(res.Errors, fmt.Sprintf("delivery failure: %s", err))
					return 0
				}
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("X-TARGET-TABLE", eventSet)
				req.Header.Set("X-NILE-PARSED", "1")

				resp, err := client.Do(req)
				if err != nil {
					res.Errors = append(res.Errors, fmt.Sprintf("delivery failure: %s", err))
					return 0
				}
				// drain response
				io.Copy(ioutil.Discard, resp.Body)
				resp.Body.Close()

				return batchCnt
			}

			for {
				select {
				case event, chanOpen := <-resp.Output:
					if !chanOpen {
						res.Count += flushBuffer()
						return
					}

					if event == nil {
						zap.S().Infof("skip sending empty event")
						continue
					}

					if len(eventSet) == 0 {
						eventSet = event.EventSet
					}

					m := make(map[string]interface{}, len(event.Fields)+3)
					for k, v := range event.Fields {
						m[k] = v
					}

					m["_datatype"] = event.Datatype

					if len(event.Message) > 0 {
						m["_message"] = event.Message
					}

					if !event.Time.IsZero() {
						m["_time"] = event.Time.UnixNano() / 1000
					}

					buffer = append(buffer, m)
					messageSize += len(event.Message)

					if messageSize < maxBufferSize && len(buffer) < batchSize {
						continue
					}

					res.Count += flushBuffer()
				case <-terminate:
					res.Count += flushBuffer()
					return
				}
			}
		}()

		select {
		case <-terminate:
		case <-ctx.Done():
			return ctx.Err()
		}

		res.Errors = getFirstErrors(res.Errors)
		if data, err := jsoniter.Marshal(res); err == nil {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write(data)
		} else {
			encodeError(ctx, errors.New("error in encoding response"), w)
		}
		return nil
	}
}

func encodeError(_ context.Context, err error, w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(codeFrom(err))
	jsoniter.NewEncoder(w).Encode(map[string]interface{}{
		"error": err.Error(),
	})
}

func codeFrom(err error) int {
	switch err {
	default:
		return http.StatusInternalServerError
	}
}

func logError(_ context.Context, err error) {
	zap.S().Infow("server transport error", "error", err)
}

func getFirstErrors(errs []string) []string {
	var uniqErrs []string

getFirstErrors:
	for _, e1 := range errs {
		notHas := true
		for _, e2 := range uniqErrs {
			if e1 == e2 {
				notHas = false
				break
			}
		}

		if notHas {
			uniqErrs = append(uniqErrs, e1)
			if len(e1) == 5 {
				break getFirstErrors
			}
		}
	}

	return uniqErrs
}
