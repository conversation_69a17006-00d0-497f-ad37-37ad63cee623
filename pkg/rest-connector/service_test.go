/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package restconnector

import (
	"bytes"
	"context"
	"net/url"
	"nile/pkg/stonewave"
	"nile/pkg/transform"
	"strings"
	"testing"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPreview(t *testing.T) {
	dm := stonewave.NewDefaultDatatypeMap()
	svc, err := NewGDIService(dm, 1)
	assert.NoError(t, err)

	testcases := []struct {
		name   string
		data   string
		o      GDIOption
		fields map[string]interface{}
	}{
		{
			name: "json",
			data: `{"a":1}`,
			o: GDIOption{
				datatype: "json",
				metadata: map[string][]string{
					"event_set": {"test_event_set"},
				},
			},
			fields: map[string]interface{}{
				"_time": float64(0),
				"a":     float64(1),
			},
		},
		{
			name: "syslog",
			data: `<68>2 2021-03-30T15:11:02.499Z directbest-of-breed.io error 1503 ID285 - We need to copy the back-end COM application!`,
			o: GDIOption{
				datatype: "syslog",
			},
			fields: map[string]interface{}{
				"_time":   float64(time.Date(2021, 3, 30, 15, 11, 2, 499000000, time.UTC).UnixNano() / 1000),
				"pri":     "68",
				"host":    "directbest-of-breed.io",
				"message": "We need to copy the back-end COM application!",
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			tc.o.r = strings.NewReader(tc.data)
			result, err := svc.Ingest(context.TODO(), &tc.o)
			assert.NoError(t, err)

			var m map[string]interface{}
			jsoniter.Unmarshal(getPreviewResult(result), &m)
			assert.Equal(t, tc.data, m["_message"])
			for k, v := range tc.fields {
				assert.Equal(t, v, m[k])
			}
		})
	}
}

func getPreviewResult(output <-chan *transform.Event) []byte {
	var result bytes.Buffer
	for event := range output {
		result.Write(event.ToPreviewBytes())
	}
	return result.Bytes()
}

func TestAddMetadata(t *testing.T) {
	testCases := []struct {
		name  string
		query string
		event transform.Event
	}{
		{
			name:  "empty",
			query: "",
			event: transform.Event{
				EventSet: "",
				Fields:   map[string]interface{}{},
			},
		},
		{
			name:  "set event_set, source, host",
			query: "ingestion_time_extraction=csv&delimiter=%2C&timestamp_config=auto&event_set=cities_csv&host=ZhangSans-MacBook-Pro.local&source=cities.txt",
			event: transform.Event{
				EventSet: "cities_csv",
				Fields: map[string]interface{}{
					"_source": "cities.txt",
					"_host":   "ZhangSans-MacBook-Pro.local",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			event := transform.Event{
				Fields: make(map[string]interface{}),
			}

			m, err := url.ParseQuery(tc.query)
			require.NoError(t, err)

			addMetadata(&event, &GDIOption{
				metadata: map[string][]string(m),
			})

			require.Equal(t, tc.event, event)
		})
	}
}

func TestParseCSVFields(t *testing.T) {
	testCases := []struct {
		name    string
		content string
		fields  []string
	}{
		{
			name:    "empty",
			content: "",
			fields:  nil,
		},
		{
			name: "basic",
			content: `a,b,c
1,2,3`,
			fields: []string{"a", "b", "c"},
		},
		{
			name: "comment",
			content: `# Hello
# This is just comments

a,b,c
1,2,3`,
			fields: []string{"a", "b", "c"},
		},
		{
			name: "only comments",
			content: `# Hello
# This is just comments

#abc
#123`,
			fields: nil,
		},
		{
			name: "header in comment",
			content: `# Hello
# This is just comments

#a,b,c
e,f,g
1,2,3`,
			fields: []string{"a", "b", "c"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := strings.NewReader(tc.content)
			parsed := parseCSVFields(r, ',')
			require.Equal(t, len(tc.fields), len(parsed))
			for i, f := range tc.fields {
				require.Equal(t, f, parsed[i])
			}
		})
	}
}
