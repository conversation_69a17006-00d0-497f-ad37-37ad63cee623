/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package kafka

import (
	"nile/pkg/stonewave"
	"nile/pkg/transform"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestMergedWorkflow(t *testing.T) {
	testCases := []struct {
		name  string
		event transform.Event
		want  transform.Event
	}{
		{
			name: "plain_text",
			event: transform.Event{
				Time:     time.Unix(887, 804),
				Message:  "All of humanity's problems stem from man's inability to sit quietly in a room alone.",
				Datatype: "plain_test",
				EventSet: "test_plain_text",
			},
			want: transform.Event{
				Time:     time.Unix(887, 804),
				Message:  "All of humanity's problems stem from man's inability to sit quietly in a room alone.",
				Datatype: "plain_test",
				EventSet: "test_plain_text",
			},
		},
		{
			name: "json",
			event: transform.Event{
				Time:     transform.TimeZero,
				Message:  `{"test": "passed"}`,
				Datatype: "json",
				EventSet: "test_json",
			},
			want: transform.Event{
				Time:     transform.TimeZero,
				Message:  `{"test": "passed"}`,
				Datatype: "json",
				EventSet: "test_json",
				Fields: map[string]interface{}{
					"test": "passed",
				},
			},
		},
	}

	dm := stonewave.NewDefaultDatatypeMap()
	mwf := newMergedWorkflow(dm)
	defer mwf.Close()

	input := make(chan *transform.Event, 1)
	output := make(chan *transform.Event, 1)
	defer close(input)
	defer close(output)

	mwf.Build(input, output)

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			input <- &tc.event
			event, ok := <-output
			assert.True(t, ok)
			assert.Equal(t, tc.want, *event)
		})
	}
}
