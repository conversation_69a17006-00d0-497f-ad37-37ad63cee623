/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package kafka

import (
	"fmt"
	"nile/pkg/transform"
	"strings"
	"unsafe"

	confluent "github.com/confluentinc/confluent-kafka-go/v2/kafka"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

var jsonAPI = jsoniter.Config{
	EscapeHTML: true,
	UseNumber:  true,
}.Froze()

var (
	removeField  string = ""
	timeField    string = "_time"
	messageField string = "_message"

	fieldMapping = map[string]map[string]string{
		"default": {
			"message":    messageField,
			"log":        messageField,
			"@timestamp": timeField,
			"@version":   removeField,
		},
		"fluentd_json": {
			"message": messageField,
		},
		"logstash_json": {
			"message":    messageField,
			"@timestamp": timeField,
			"@version":   removeField,
		},
		"fluentbit_json": {
			"log":        messageField,
			"@timestamp": timeField,
		},
		"vector_json": {
			"message":   messageField,
			"timestamp": timeField,
		},
	}
)

func GenerateEventFromMessage(msg *confluent.Message) *transform.Event {
	if msg == nil {
		return nil
	}

	event := transform.NewEvent()
	if idx := strings.Index(*msg.TopicPartition.Topic, ".gdi."); idx >= 0 {
		event.EventSet = (*msg.TopicPartition.Topic)[idx+5:]
	} else {
		return nil
	}

	var m map[string]interface{}
	if err := jsonAPI.Unmarshal(msg.Value, &m); err != nil {
		// input msg is not a valid json
		event.Message = string(msg.Value)
		return event
	}

	rt, ok := m["_record_type"].(string)
	if ok {
		delete(m, "_record_type")
	} else {
		rt = "default"
	}

	var ignoreTimestamp bool
	if it, ok := m["_ignore_timestamp"].(string); ok {
		ignoreTimestamp = strings.ToUpper(it) == "TRUE"
		delete(m, "_ignore_timestamp")
	}

	fm := fieldMapping[rt]

	for k, v := range m {
		if fm != nil {
			if mk, ok := fm[k]; ok {
				k = mk
			}
		}
		event.SetField(k, v)
	}

	if ignoreTimestamp {
		event.Time = transform.TimeZero
	}

	return event
}

func GenerateMessageFromEvent(event *transform.Event) *confluent.Message {
	if event == nil {
		return nil
	}

	topic := IDXTopicPrefix + event.EventSet

	m := make(map[string]interface{}, len(event.Fields)+3)
	for k, v := range event.Fields {
		m[k] = v
	}

	m["_datatype"] = event.Datatype

	if len(event.Message) > 0 {
		m["_message"] = event.Message
	}

	if !event.Time.IsZero() {
		m["_time"] = event.Time.UnixNano() / 1000
	}

	data, err := jsonAPI.Marshal(m)
	if err != nil {
		zap.S().Infow("fail to generate message from event", "error", err)
		return nil
	}

	// headers := make([]confluent.Header, 1, len(event.Fields)+2)
	// headers[0] = confluent.Header{
	// 	Key:   "_datatype",
	// 	Value: transform.EncodeValue(event.Datatype),
	// }

	// if !event.Time.IsZero() {
	// 	headers = append(headers,
	// 		confluent.Header{
	// 			Key:   "_time",
	// 			Value: []byte(fmt.Sprintf("%d", (event.Time.UnixNano() / 1000))),
	// 		},
	// 	)
	// }

	// for k, v := range event.Fields {
	// 	if len(k) > 0 {
	// 		headers = append(headers, confluent.Header{
	// 			Key:   k,
	// 			Value: transform.EncodeValue(v),
	// 		})
	// 	}
	// }

	msg := confluent.Message{
		TopicPartition: confluent.TopicPartition{
			Topic:     &topic,
			Partition: confluent.PartitionAny,
		},
		// Value: *(*[]byte)(unsafe.Pointer(&event.Message)),
		Value: data,
		// Headers: headers,
	}

	return &msg
}

func GenerateMessageWithHeaderFromEvent(event *transform.Event) *confluent.Message {
	if event == nil {
		return nil
	}

	topic := IDXTopicPrefix + event.EventSet

	headers := make([]confluent.Header, 1, len(event.Fields)+2)
	headers[0] = confluent.Header{
		Key:   "_datatype",
		Value: transform.EncodeValue(event.Datatype),
	}

	if !event.Time.IsZero() {
		headers = append(headers,
			confluent.Header{
				Key:   "_time",
				Value: []byte(fmt.Sprintf("%d", (event.Time.UnixNano() / 1000))),
			},
		)
	}

	for k, v := range event.Fields {
		if len(k) > 0 {
			headers = append(headers, confluent.Header{
				Key:   k,
				Value: transform.EncodeValue(v),
			})
		}
	}

	msg := confluent.Message{
		TopicPartition: confluent.TopicPartition{
			Topic:     &topic,
			Partition: confluent.PartitionAny,
		},
		Value:   *(*[]byte)(unsafe.Pointer(&event.Message)),
		Headers: headers,
	}

	return &msg
}
