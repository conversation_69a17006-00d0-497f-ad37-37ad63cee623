/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package kafka

import (
	"context"
	"errors"
	"fmt"
	"nile/pkg/stonewave"
	"nile/pkg/transform"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/Shopify/sarama"
	confluent "github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/spf13/viper"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

const bufferSize = 8

var GDITopicPrefix = "yh.gdi."
var IDXTopicPrefix = "yh.idx."

var ErrNoEventToCommit = errors.New("no new event to commit")
var ErrFlushTimeout = errors.New("flush producer timeout")

var disableTxnLock = os.Getenv("KAFKA_DISABLE_TXN_LOCK") == "true"

type Transformer struct {
	brokers            string
	topicNamespace     string
	group              string
	consumer           *confluent.Consumer
	processors         map[string]map[int32]*gdiProcessor
	dm                 *stonewave.DatatypeMap
	needUpdateDatatype atomic.Bool
	saramaAdmin        sarama.ClusterAdmin
	enableTransaction  bool
}

func NewTransformer(brokers string, topicNamespace string, group string, dm *stonewave.DatatypeMap, enableTrasction bool) (*Transformer, error) {
	instanceId := os.Getenv("KAFKA_GROUP_INSTANCE_ID")
	if instanceId != "" {
		zap.S().Infow("kafka consumer running as static member", "group.instance.id", instanceId)
	}

	tf := &Transformer{
		brokers:           brokers,
		topicNamespace:    topicNamespace,
		group:             group,
		processors:        make(map[string]map[int32]*gdiProcessor),
		dm:                dm,
		enableTransaction: enableTrasction,
	}

	sc := sarama.NewConfig()
	sc.Version = sarama.V2_8_0_0
	sc.ClientID = "nile-admin"
	sc.Metadata.AllowAutoTopicCreation = false

	ca, err := sarama.NewClusterAdmin(strings.Split(brokers, ","), sc)
	if err != nil {
		return nil, err
	}
	tf.saramaAdmin = ca

	// Helper functions for viper config with default value
	getStringOrDefault := func(key, def string) string {
		v := viper.GetString(key)
		if v == "" {
			return def
		}
		return v
	}

	getIntOrDefault := func(key string, def int) int {
		if !viper.IsSet(key) {
			return def
		}
		v := viper.GetInt(key)
		if v == 0 {
			return def
		}
		return v
	}

	getBoolOrDefault := func(key string, def bool) bool {
		if !viper.IsSet(key) {
			return def
		}
		return viper.GetBool(key)
	}

	// Build consumerConfig, all parameters are read from viper, with default values as fallback
	consumerConfig := &confluent.ConfigMap{
		"client.id":                          getStringOrDefault("kafka.consumer.client.id", "nile-transformer"),
		"bootstrap.servers":                  tf.brokers,
		"group.id":                           tf.group,
		"group.instance.id":                  getStringOrDefault("kafka.consumer.group.instance.id", os.Getenv("KAFKA_GROUP_INSTANCE_ID")),
		"partition.assignment.strategy":      getStringOrDefault("kafka.consumer.partition.assignment.strategy", "roundrobin"),
		"allow.auto.create.topics":           getBoolOrDefault("kafka.consumer.allow.auto.create.topics", false),
		"auto.offset.reset":                  getStringOrDefault("kafka.consumer.auto.offset.reset", "earliest"),
		"enable.auto.commit":                 getBoolOrDefault("kafka.consumer.enable.auto.commit", false),
		"topic.metadata.refresh.interval.ms": getIntOrDefault("kafka.consumer.topic.metadata.refresh.interval.ms", 30),
		"message.max.bytes":                  getIntOrDefault("kafka.consumer.message.max.bytes", viper.GetInt("max-message-bytes")),
		"max.poll.interval.ms":               getIntOrDefault("kafka.consumer.max.poll.interval.ms", 300000),
		"session.timeout.ms":                 getIntOrDefault("kafka.consumer.session.timeout.ms", 45000),
	}

	tf.consumer, err = confluent.NewConsumer(consumerConfig)
	if err != nil {
		return nil, err
	}

	inputTopic := fmt.Sprintf("^%s\\.gdi\\..*", tf.topicNamespace)
	err = tf.consumer.Subscribe(inputTopic, tf.groupRebalance)
	if err != nil {
		return nil, err
	}

	dm.RegisterListener(tf)

	return tf, nil
}

func (tf *Transformer) Close() (ret error) {
	tf.dm.UnRegisterListener(tf)
	if err := tf.consumer.Close(); err != nil {
		ret = err
	}
	if err := tf.saramaAdmin.Close(); err != nil {
		ret = err
	}
	return
}

func (tf *Transformer) UpdateDatatype(datatype *stonewave.Datatype) error {
	tf.needUpdateDatatype.Store(true)
	return nil
}

func (tf *Transformer) RemoveDatatype(name string) error {
	tf.needUpdateDatatype.Store(true)
	return nil
}

func (tf *Transformer) getProcessor(topic string, par int32) (*gdiProcessor, error) {
	if _, ok := tf.processors[topic]; !ok {
		tf.processors[topic] = make(map[int32]*gdiProcessor)
	}

	p, ok := tf.processors[topic][par]
	if ok {
		return p, nil
	}

	// lazy create processor
	var err error
	p, err = tf.newGdiProcessor(topic, par, tf.enableTransaction)
	if err != nil {
		return nil, err
	}

	tf.processors[topic][par] = p

	return p, nil
}

func (tf *Transformer) groupRebalance(consumer *confluent.Consumer, event confluent.Event) error {
	zap.S().Infow("transformer rebalance event", "event", event)

	switch e := event.(type) {
	case confluent.AssignedPartitions:
		err := consumer.Assign(e.Partitions)
		if err != nil {
			panic(err)
		}

	case confluent.RevokedPartitions:
		for topic, pars := range tf.processors {
			for partition, gp := range pars {
				zap.S().Debugw("destroy gdi processor", "topic", topic, "partition", partition)
				gp.Destroy()
			}
		}

		// Clear producer
		tf.processors = make(map[string]map[int32]*gdiProcessor)

		err := consumer.Unassign()
		if err != nil {
			panic(err)
		}
	}

	return nil
}

func (tf *Transformer) MakeConsumeWithContext(ctx context.Context) func() error {
	return func() error {
		commitInterval := 1 * time.Second
		maxIdle := 300 // 300 commits = 5 min

		txnCommitTimer := time.NewTimer(commitInterval)
		defer txnCommitTimer.Stop()

		// delete consumed records
		cleanTicker := time.NewTicker(5 * time.Minute)
		defer cleanTicker.Stop()

		heartbeat := make(chan interface{})
		defer close(heartbeat)

		go func() {
			for {
				timeout := time.NewTimer(15 * time.Minute)
				select {
				case _, ok := <-heartbeat:
					if !ok {
						return
					}
				case <-timeout.C:
					panic("no consumer hearbeat for 15 minutes")
				}
				timeout.Stop()
			}
		}()

		for {
			// send heartbeat
			select {
			case heartbeat <- struct{}{}:
			default:
			}

			select {
			case <-ctx.Done():
				if err := tf.Close(); err != nil {
					zap.S().Errorw("fail to close consumer", "error", err)
				} else {
					zap.S().Info("consumer closed")
				}
				return nil

			case <-cleanTicker.C:
				partitions, err := tf.consumer.Assignment()
				if err != nil {
					zap.S().Errorw("fail to get consumer assignment", "error", err)
					continue
				}

				offsets, err := tf.consumer.Committed(partitions, 30000)
				if err != nil {
					zap.S().Errorw("fail to retrieves committed offsets, skip record deletion", "error", err)
					continue
				}

				for i := range offsets {
					topic := *(offsets[i].Topic)
					p := offsets[i].Partition
					low, _, err := tf.consumer.GetWatermarkOffsets(topic, p)
					if err != nil {
						zap.S().Errorw("fail to get watermark offset", "topic", topic, "partition", p, "error", err)
						continue
					}

					if int64(offsets[i].Offset) > low {
						// Always create a new saramaAdmin for each delete operation
						sc := sarama.NewConfig()
						sc.Version = sarama.V2_8_0_0
						sc.ClientID = "nile-admin"
						sc.Metadata.AllowAutoTopicCreation = false
						admin, err := sarama.NewClusterAdmin(strings.Split(tf.brokers, ","), sc)
						if err != nil {
							zap.S().Errorw("fail to create new sarama admin for delete records", "error", err)
							continue
						}
						// Exponential backoff retry for DeleteRecords
						var lastErr error
						for attempt := 0; attempt < 5; attempt++ {
							err = admin.DeleteRecords(*offsets[i].Topic, map[int32]int64{offsets[i].Partition: int64(offsets[i].Offset)})
							if err == nil {
								break
							}
							lastErr = err
							backoff := time.Duration(1<<attempt) * 100 * time.Millisecond // 100ms, 200ms, 400ms, 800ms, 1600ms
							zap.S().Errorw("fail to delete records, will retry", "topic", *offsets[i].Topic, "partition", offsets[i].Partition, "error", err, "attempt", attempt+1, "backoff", backoff)
							time.Sleep(backoff)
						}
						if lastErr != nil && err != nil {
							zap.S().Errorw("fail to delete records after retries", "topic", *offsets[i].Topic, "partition", offsets[i].Partition, "error", err)
						}
						admin.Close()
					}
				}

			case <-txnCommitTimer.C:
				var rewindToppars []confluent.TopicPartition
				needUpdate := tf.needUpdateDatatype.Swap(false)

				for topic, pars := range tf.processors {
					for partition, gp := range pars {
						// update merged workflow if datatype catalog changes
						if needUpdate {
							gp.UpdateWorkflows()
						}

						// commit transaction
						if err := gp.tryCommit(); err != nil {
							if err == ErrNoEventToCommit {
								if gp.idleCommits > maxIdle {
									zap.S().Debugw("delete idle gdi processor", "topic", topic, "partition", partition)
									delete(pars, partition)
									gp.Destroy()
								}
								continue
							}

							zap.S().Errorw("fail to commit transaction", "topic", topic, "gp.topic", gp.topic, "partition", partition, "gp.partition", gp.partition, "error", err)

							if err := gp.AbortTransaction(); err != nil {
								// destroy gdi processor if abort transaction fails
								zap.S().Errorw("fail in abort transaction", "topic", topic, "parition", partition, "error", err)
								delete(pars, partition)
								gp.Destroy()
							} else {
								// quit go routines which send records to producer channel
								gp.DestroyPipeline()
								if err := gp.BeginTransaction(); err != nil {
									return fmt.Errorf("fail in begin new transaction: %w", err)
								}
								// Recreate pipeline goroutinues
								gp.InitPipeline()
							}

							zap.S().Infow("append rewind toppar", "topic", topic, "partition", "partition")
							theTopic := topic // for &theTopic in rewindToppars
							rewindToppars = append(rewindToppars, confluent.TopicPartition{Topic: &theTopic, Partition: partition})
						}
					}
				}

				if len(rewindToppars) > 0 {
					zap.S().Debugw("rewind toppars", "toppars", rewindToppars)

					committed, err := tf.consumer.Committed(rewindToppars, 60*1000)
					if err != nil {
						return fmt.Errorf("faiil in fetch commit: %w", err)
					}

					// rewind consumer offset for failed gdiProcessor
					for _, tp := range committed {
						if tp.Offset < 0 {
							// No committed offset, reset to earliest
							tp.Offset = confluent.OffsetBeginning
						}

						zap.S().Infow("rewinding input partition", "topic", tp.Topic, "partition", tp.Partition, "offset", tp.Offset)
						err = tf.consumer.Seek(tp, -1)
						if err != nil {
							return fmt.Errorf("fail in consumer seek: %w", err)
						}
					}
				}

				// restart transaction commit timer
				txnCommitTimer.Reset(commitInterval)

			default:
				// Poll consumer for new messages or rebalance events.
				ev := tf.consumer.Poll(60000)
				if ev == nil {
					continue
				}

				switch e := ev.(type) {
				case *confluent.Message:
					srcTopic := *(e.TopicPartition.Topic)
					p, err := tf.getProcessor(srcTopic, e.TopicPartition.Partition)
					if err != nil {
						return err
					}

					p.Input() <- e

				case confluent.Error:
					// Errors are generally just informational.
					zap.S().Debugw("consumer error", "error", ev)

				default:
					zap.S().Infow("consumer event ignored", "event", ev)
				}
			}
		}
	}
}

func createTransactionalProducer(brokers string, topic string, par int32) (*confluent.Producer, error) {
	producerConfig := &confluent.ConfigMap{
		"client.id":                  "nile-transformer",
		"bootstrap.servers":          brokers,
		"transactional.id":           fmt.Sprintf("nile-transformer-%s-%d", topic, par),
		"transaction.timeout.ms":     600000, // 10 minutes
		"allow.auto.create.topics":   false,
		"compression.type":           "lz4",
		"linger.ms":                  100,
		"queue.buffering.max.kbytes": viper.GetInt("queue-buffering-max-kbytes"),
		"go.produce.channel.size":    viper.GetInt("producer-channel-size"),
		"go.delivery.reports":        false,
		"message.max.bytes":          viper.GetInt("max-message-bytes"),
	}

	producer, err := confluent.NewProducer(producerConfig)
	if err != nil {
		return nil, err
	}

	go func() {
		for e := range producer.Events() {
			switch ev := e.(type) {
			// Message delivery report
			case *confluent.Message:
				if ev.TopicPartition.Error != nil {
					zap.S().Infow("delivery failure", "producer", producer.String(), "error", ev.TopicPartition.Error)
				}

			case confluent.Error:
				zap.S().Infow("confluent error", "error", e)

			default:
				zap.S().Infow("ignore event", "event", ev)
			}
		}
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = producer.InitTransactions(ctx)
	if err != nil {
		producer.Close()
		return nil, err
	}

	err = producer.BeginTransaction()
	if err != nil {
		producer.Close()
		return nil, err
	}

	return producer, nil
}

func createProducer(brokers string, topic string, par int32) (*confluent.Producer, error) {
	zap.S().Debug("create non-transactional producer", "topic", topic, "partition", par)

	producerConfig := &confluent.ConfigMap{
		"client.id":                  "nile-transformer",
		"bootstrap.servers":          brokers,
		"allow.auto.create.topics":   false,
		"compression.type":           "lz4",
		"linger.ms":                  100,
		"queue.buffering.max.kbytes": viper.GetInt("queue-buffering-max-kbytes"),
		"message.max.bytes":          viper.GetInt("max-message-bytes"),
	}

	producer, err := confluent.NewProducer(producerConfig)
	if err != nil {
		return nil, err
	}

	go func() {
		for e := range producer.Events() {
			switch ev := e.(type) {
			// Message delivery report
			case *confluent.Message:
				if ev.TopicPartition.Error != nil {
					zap.S().Panic("delivery failure", "producer", producer.String(), "error", ev.TopicPartition.Error)
				}

			case confluent.Error:
				zap.S().Infow("confluent error", "error", e)

			default:
				zap.S().Infow("ignore event", "event", ev)
			}
		}
	}()

	return producer, nil
}

// destroyTransactionalProducer aborts the current transaction and destroys the producer.
func destroyTransactionalProducer(producer *confluent.Producer) error {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	err := producer.AbortTransaction(ctx)
	if err != nil && err.(confluent.Error).Code() == confluent.ErrState {
		// No transaction in progress, ignore the error.
		err = nil
	}

	producer.Close()
	return err
}

type gdiProcessor struct {
	parent      *Transformer
	topic       string
	partition   int32
	enableTxn   bool
	offset      confluent.Offset
	producer    *confluent.Producer
	idleCommits int

	wg  sync.WaitGroup
	mwf *mergedWorkflow

	input         chan *confluent.Message
	processInput  chan *transform.Event
	processOutput chan *transform.Event

	destroyed chan struct{}

	mu sync.Mutex
}

func (tf *Transformer) newGdiProcessor(topic string, par int32, enableTxn bool) (*gdiProcessor, error) {
	var producer *confluent.Producer
	var err error
	if enableTxn {
		producer, err = createTransactionalProducer(tf.brokers, topic, par)
	} else {
		producer, err = createProducer(tf.brokers, topic, par)
	}
	if err != nil {
		return nil, err
	}

	gp := &gdiProcessor{
		parent:    tf,
		topic:     topic,
		partition: par,
		offset:    confluent.OffsetInvalid,
		producer:  producer,
		mwf:       newMergedWorkflow(tf.dm),
		enableTxn: enableTxn,
	}

	gp.InitPipeline()

	return gp, nil
}

func (gp *gdiProcessor) UpdateWorkflows() {
	gp.mwf.Build(gp.processInput, gp.processOutput)
}

func (gp *gdiProcessor) InitPipeline() {
	gp.input = make(chan *confluent.Message, bufferSize)
	gp.processInput = make(chan *transform.Event, bufferSize)
	gp.processOutput = make(chan *transform.Event, bufferSize)

	gp.destroyed = make(chan struct{})

	gp.offset = confluent.OffsetInvalid

	gp.wg.Add(2)
	go gp.parseMessage()
	gp.mwf.Build(gp.processInput, gp.processOutput)
	go gp.sendMessage()
}

func (gp *gdiProcessor) DestroyPipeline() {
	close(gp.input)
	close(gp.processOutput)
	close(gp.destroyed)

	waitOrTimeout := make(chan struct{})
	go func() {
		defer close(waitOrTimeout)
		gp.wg.Wait()
	}()

	select {
	case <-waitOrTimeout:
		return
	case <-time.After(300 * time.Second):
		panic("timeout in destroy pipeline")
	}
}

func (gp *gdiProcessor) Destroy() {
	gp.DestroyPipeline()

	if gp.enableTxn {
		if err := destroyTransactionalProducer(gp.producer); err != nil {
			zap.S().Infow("fail to destroy transactional producer", "topic", gp.topic, "parition", gp.partition, "error", err)
		}
	} else {
		gp.producer.Close()
	}
}

func (gp *gdiProcessor) setOffset(offset confluent.Offset) {
	gp.offset = offset
}

func (gp *gdiProcessor) parseMessage() {
	defer gp.wg.Done()
	defer close(gp.processInput)

	for msg := range gp.input {
		event := GenerateEventFromMessage(msg)
		if event == nil {
			zap.S().Debugw("skip empty raw message", "topic", msg.TopicPartition.Topic, "partition", msg.TopicPartition.Partition)
			continue
		}

		event.Metadata = msg.TopicPartition
		select {
		case gp.processInput <- event:
		case <-gp.destroyed:
		}
	}
}

func (gp *gdiProcessor) sendMessage() {
	defer gp.wg.Done()
	for event := range gp.processOutput {
		if sendMsg := GenerateMessageFromEvent(event); sendMsg != nil {
			gp.mu.Lock()
			select {
			case gp.producer.ProduceChannel() <- sendMsg:
			case <-gp.destroyed:
			}
			gp.setOffset(event.Metadata.(confluent.TopicPartition).Offset)
			gp.mu.Unlock()
		}
	}
}

func (gp *gdiProcessor) Input() chan<- *confluent.Message {
	return gp.input
}

func (gp *gdiProcessor) AbortTransaction() error {
	if !gp.enableTxn {
		return nil
	}

	if !disableTxnLock {
		gp.mu.Lock()
		defer gp.mu.Unlock()
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	err := gp.producer.AbortTransaction(ctx)
	return err
}

func (gp *gdiProcessor) BeginTransaction() error {
	if !gp.enableTxn {
		return nil
	}

	if !disableTxnLock {
		gp.mu.Lock()
		defer gp.mu.Unlock()
	}

	return gp.producer.BeginTransaction()
}

func (gp *gdiProcessor) tryCommit() error {
	gp.mu.Lock()
	defer gp.mu.Unlock()

	if gp.offset == confluent.OffsetInvalid {
		gp.idleCommits++
		return ErrNoEventToCommit
	}
	gp.idleCommits = 0

	if remain := gp.producer.Flush(60000); remain > 0 {
		return ErrFlushTimeout
	}

	consumerMetadata, err := gp.parent.consumer.GetConsumerGroupMetadata()
	if err != nil {
		return err
	}

	offsets := []confluent.TopicPartition{
		{
			Topic:     &gp.topic,
			Partition: gp.partition,
			Offset:    gp.offset + 1,
		},
	}

	if !gp.enableTxn {
		_, err = gp.parent.consumer.CommitOffsets(offsets)
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(1*time.Minute))
	defer cancel()

	err = gp.producer.SendOffsetsToTransaction(ctx, offsets, consumerMetadata)
	if err != nil {
		return err
	}

	err = gp.producer.CommitTransaction(ctx)
	if err != nil {
		return err
	}

	gp.offset = confluent.OffsetInvalid

	// Inline BeginTransaction to avoid deadlock, as we are already holding the lock.
	if gp.enableTxn {
		err = gp.producer.BeginTransaction()
		if err != nil {
			return err
		}
	}

	return nil
}

type mergedWorkflow struct {
	dm    *stonewave.DatatypeMap
	group *errgroup.Group
	term  chan struct{}
}

func newMergedWorkflow(dm *stonewave.DatatypeMap) *mergedWorkflow {
	mwf := mergedWorkflow{
		dm: dm,
	}

	return &mwf
}

func (mwf *mergedWorkflow) Build(in chan *transform.Event, out chan *transform.Event) {
	if mwf.term != nil {
		mwf.Close()
	}

	// get workflows from datamap
	workflows := make(map[string]*transform.Workflow)
	for iter := range mwf.dm.IterBuffered() {
		name := iter.Key
		datatype := iter.Val.(stonewave.Datatype)
		workflows[name] = transform.NewWorkflowForDatatype(&datatype)
	}

	mwf.group = new(errgroup.Group)
	mwf.term = make(chan struct{})

	var input chan *transform.Event
	var output chan *transform.Event = in
	var lastStage map[string]transform.Processor

	for idx := 0; ; idx++ {
		processors := make(map[string]transform.Processor)
		for datatype, wf := range workflows {
			nc := wf.GetNodeConfig()
			if len(nc) > idx {
				name, options := transform.GetNodeConfig(nc[idx])
				pb, ok := transform.BuilderManager.Processor[name]
				if !ok {
					zap.S().Infow("no processor builder", "type", name)
					continue
				}

				proc, err := pb(options)
				if err != nil {
					zap.S().Infow("fail in create processor", "type", name, "options", options)
					continue
				}

				processors[datatype] = proc
			}
		}

		if len(processors) == 0 {
			break
		}

		if len(lastStage) > 0 {
			output = make(chan *transform.Event, bufferSize)
			if input == in {
				mwf.group.Go(makeProcessFunc(input, output, lastStage, mwf.term, false))
			} else {
				mwf.group.Go(makeProcessFunc(input, output, lastStage, nil, false))
			}
		}

		lastStage = processors
		input = output
	}

	if len(lastStage) > 0 {
		if input == in {
			mwf.group.Go(makeProcessFunc(input, out, lastStage, mwf.term, true))
		} else {
			mwf.group.Go(makeProcessFunc(input, out, lastStage, nil, true))
		}
	}
}

func makeProcessFunc(
	in chan *transform.Event, out chan *transform.Event, processors map[string]transform.Processor,
	term chan struct{}, last bool) func() error {

	if term == nil {
		return func() error {
			defer func() {
				recover()

				if !last {
					close(out)
				}
			}()

			for event := range in {
				if proc, has := processors[event.Datatype]; has {
					event, _ = proc.Process(event)
				}
				if event != nil {
					out <- event
				}
			}
			return nil
		}
	}

	return func() error {
		defer func() {
			recover()

			if !last {
				close(out)
			}
		}()

		for {
			select {
			case event, chanOpen := <-in:
				if !chanOpen {
					return nil
				}
				if proc, has := processors[event.Datatype]; has {
					event, _ = proc.Process(event)
				}
				if event != nil {
					out <- event
				}
			case <-term:
				return nil
			}
		}
	}
}

// Close WorkGroup by closing all workflows and wait for their termination. It then close the output channel
func (mwf *mergedWorkflow) Close() {
	close(mwf.term)
	mwf.term = nil
	mwf.group.Wait()
}

func (mwf *mergedWorkflow) Wait() {
	mwf.group.Wait()
}
