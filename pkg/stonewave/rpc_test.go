/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package stonewave

import (
	"context"
	"flag"
	"testing"

	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

var stonewave = flag.Bool("stonewave", false, "true to run stonewave tests")
var swIP = flag.String("host", "************", "stonewave host")
var swPort = flag.String("port", "9972", "stonewave port")

func TestNewClient(t *testing.T) {
	if !*stonewave {
		t.Skip("skip stonewave test")
	}

	client, err := NewStonewaveClient(*swIP, *swPort)
	assert.Nil(t, err, "new stonewave client no error")
	assert.NotNil(t, client, "stonewave client is created")
}

func TestGetCatalog(t *testing.T) {
	if !*stonewave {
		t.Skip("skip stonewave test")
	}

	client, err := NewStonewaveClient(*swIP, *swPort)
	assert.Nil(t, err, "new stonewave client no error")

	data, err := client.GetCatalog(context.Background(), "DATATYPE", "")
	assert.Nil(t, err, "get catalog has no error")
	assert.NotNil(t, data)
}

func TestGetDataType(t *testing.T) {
	if !*stonewave {
		t.Skip("skip stonewave test")
	}

	testcases := []struct {
		name                    string
		datatype                string
		ingestionTimeExtraction string
		delimiter               string
		timestampConfig         string
		fieldNames              []string
		discardRawMessage       bool
	}{
		{
			name:                    "csv",
			datatype:                "csv",
			ingestionTimeExtraction: "csv",
			delimiter:               ",",
			timestampConfig:         "auto",
			fieldNames:              nil,
			discardRawMessage:       false,
		},
		{
			name:                    "json",
			datatype:                "json",
			ingestionTimeExtraction: "json",
			delimiter:               ",",
			timestampConfig:         "auto",
			fieldNames:              nil,
			discardRawMessage:       false,
		},
	}

	client, err := NewStonewaveClient(*swIP, *swPort)
	assert.Nil(t, err, "new stonewave client no error")

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			datatype, err := client.GetDataType(context.Background(), tc.datatype)
			assert.Nil(t, err, "get catalog has no error")
			assert.Equal(t, tc.ingestionTimeExtraction, datatype.IngestionTimeExtraction)
			assert.Equal(t, tc.delimiter, datatype.Delimiter)
			assert.Equal(t, tc.timestampConfig, datatype.TimestampConfig)
			assert.Equal(t, tc.fieldNames, datatype.IngestionTimeFieldNames)
			assert.Equal(t, tc.discardRawMessage, datatype.DiscardRawMessage)
		})
	}
}

func TestGetAllDataTypes(t *testing.T) {
	if !*stonewave {
		return
	}

	client, err := NewStonewaveClient(*swIP, *swPort)
	assert.Nil(t, err, "new stonewave client no error")

	dm, err := client.GetAllDataType(context.Background())
	assert.Nil(t, err, "get catalog has no error")
	assert.True(t, len(dm) > 0)

	defaultDm := NewDefaultDatatypeMap()
	for item := range defaultDm.IterBuffered() {
		t.Run(item.Key, func(t *testing.T) {
			assert.Equal(t, item.Val, dm[item.Key])
		})
	}
}

func TestNewDatatypeForCatalog(t *testing.T) {
	testCsaes := []struct {
		name       string
		attributes string
		want       Datatype
	}{
		{
			name:       "plain_text",
			attributes: "{\"attributes\":{\"timestamp_config\":\"auto\",\"built_in\":true,\"datatype_name\":\"plain_text\",\"last_modified_time\":1598918400000000,\"uuid\":\"0feae929-4301-540e-ba02-2dc4ab64ab23\"}}",
			want: Datatype{
				IngestionTimeExtraction: "none",
				TimestampConfig:         "auto",
				Delimiter:               ",",
			},			
		},
		{
			name:       "field names",
			attributes: "{\"attributes\":{\"ingestion_time_field_names\":[\"a\",\"b\",\"c\"],\"ingestion_time_extraction\":\"csv\",\"built_in\":false,\"datatype_name\":\"alantest\",\"last_modified_time\":1628988047220024,\"uuid\":\"ea4d669a-05ae-5cce-8d99-c1f1cc333e89\"}}",
			want: Datatype{
				IngestionTimeExtraction: "csv",
				TimestampConfig:         "auto",
				Delimiter:               ",",
				IngestionTimeFieldNames: []string{"a", "b", "c"},				
			},			
		},
	}

	for _, tc := range testCsaes {
		t.Run(tc.name, func(t *testing.T) {
			var m map[string]interface{}
			err := jsoniter.Unmarshal([]byte(tc.attributes), &m)
			assert.NoError(t, err)
			datatype := NewDatatypeForCatalog("", m)
			assert.Equal(t, tc.want, *datatype)
		})
	}
}
