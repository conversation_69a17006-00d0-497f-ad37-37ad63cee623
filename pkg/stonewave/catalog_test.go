/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package stonewave

import (
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

// MockDatatypeListener is a mock of DatatypeListener interface.
type MockDatatypeListener struct {
	ctrl     *gomock.Controller
	recorder *MockDatatypeListenerMockRecorder
}

// MockDatatypeListenerMockRecorder is the mock recorder for MockDatatypeListener.
type MockDatatypeListenerMockRecorder struct {
	mock *MockDatatypeListener
}

// NewMockDatatypeListener creates a new mock instance.
func NewMockDatatypeListener(ctrl *gomock.Controller) *MockDatatypeListener {
	mock := &MockDatatypeListener{ctrl: ctrl}
	mock.recorder = &MockDatatypeListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatatypeListener) EXPECT() *MockDatatypeListenerMockRecorder {
	return m.recorder
}

// RemoveDatatype mocks base method.
func (m *MockDatatypeListener) RemoveDatatype(name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDatatype", name)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveDatatype indicates an expected call of RemoveDatatype.
func (mr *MockDatatypeListenerMockRecorder) RemoveDatatype(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDatatype", reflect.TypeOf((*MockDatatypeListener)(nil).RemoveDatatype), name)
}

// UpdateDatatype mocks base method.
func (m *MockDatatypeListener) UpdateDatatype(arg0 *Datatype) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDatatype", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDatatype indicates an expected call of UpdateDatatype.
func (mr *MockDatatypeListenerMockRecorder) UpdateDatatype(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDatatype", reflect.TypeOf((*MockDatatypeListener)(nil).UpdateDatatype), arg0)
}

func TestDefaultCatalog(t *testing.T) {
	testcases := []struct {
		name                    string
		datatype                string
		ingestionTimeExtraction string
		delimiter               string
		timestampConfig         string
		fieldNames              []string
		discardRawMessage       bool
	}{
		{
			name:                    "csv",
			datatype:                "csv",
			ingestionTimeExtraction: "csv",
			delimiter:               ",",
			timestampConfig:         "auto",
			fieldNames:              nil,
			discardRawMessage:       false,
		},
		{
			name:                    "json",
			datatype:                "json",
			ingestionTimeExtraction: "json",
			delimiter:               ",",
			timestampConfig:         "auto",
			fieldNames:              nil,
			discardRawMessage:       false,
		},
	}

	dm := NewDefaultDatatypeMap()

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			datatype := dm.GetDatatype(tc.name)
			assert.Equal(t, tc.ingestionTimeExtraction, datatype.IngestionTimeExtraction)
			assert.Equal(t, tc.delimiter, datatype.Delimiter)
			assert.Equal(t, tc.timestampConfig, datatype.TimestampConfig)
			assert.Equal(t, tc.fieldNames, datatype.IngestionTimeFieldNames)
			assert.Equal(t, tc.discardRawMessage, datatype.DiscardRawMessage)
		})
	}
}

func TestRegisterListener(t *testing.T) {
	ctrl := gomock.NewController(t)

	dm := NewDatatypeMap()
	l1 := NewMockDatatypeListener(ctrl)
	l2 := NewMockDatatypeListener(ctrl)
	l3 := NewMockDatatypeListener(ctrl)

	dm.RegisterListener(l1)
	assert.Equal(t, 1, len(dm.listeners))
	dm.RegisterListener(l2)
	assert.Equal(t, 2, len(dm.listeners))
	dm.RegisterListener(l3)
	assert.Equal(t, 3, len(dm.listeners))
	dm.UnRegisterListener(l3)
	assert.Equal(t, 2, len(dm.listeners))

	datatype := Datatype{Name: "test_datatype"}

	l1.EXPECT().UpdateDatatype(&datatype).Times(1)
	l2.EXPECT().UpdateDatatype(&datatype).Times(1)
	l3.EXPECT().UpdateDatatype(&datatype).Times(0)

	dm.SetDatatype("test_datatype", datatype)

	ctrl.Finish()
}
