/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package stonewave

import (
	"context"
	"fmt"
	"strconv"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/viper"
	"google.golang.org/grpc/metadata"

	"github.com/apache/arrow/go/arrow/flight"
	"google.golang.org/grpc"
)

type Stonewave struct {
	client flight.Client
}

type clientAuth struct {
	token string
}

func (a *clientAuth) Authenticate(ctx context.Context, c flight.AuthConn) error {
	if err := c.Send([]byte(a.token)); err != nil {
		return err
	}

	_, err := c.Read()
	return err
}

func (a *clientAuth) GetToken(ctx context.Context) (string, error) {
	return "", nil
}

type clientAuthHeaderMiddleware string

func (cam *clientAuthHeaderMiddleware) StartCall(ctx context.Context) context.Context {
	token := viper.GetString("token")
	hdrmd := make(metadata.MD)
	hdrmd.Set("authorization", "Bearer "+token)
	return metadata.NewOutgoingContext(ctx, hdrmd)
}

func NewStonewaveClient(host string, port string) (*Stonewave, error) {
	token := viper.GetString("token")

	addr := host + ":" + port
	ca := clientAuth{token: token}
	cam := clientAuthHeaderMiddleware(token)
	cmw := flight.CreateClientMiddleware(&cam)
	client, err := flight.NewClientWithMiddleware(addr, &ca, []flight.ClientMiddleware{cmw}, grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	return &Stonewave{client: client}, nil
}

type GetDataTypeResponse struct {
	Data       map[string]interface{} `json:"data"`
	StatusCode int                    `json:"statusCode"`
}

type GetAllDataTypeResponse struct {
	Data       map[string]interface{} `json:"data"`
	StatusCode int                    `json:"statusCode"`
}

func (s Stonewave) GetCatalog(ctx context.Context, catType string, key string) ([]byte, error) {
	b := struct {
		T string `json:"catalog_type"`
		K string `json:"entry_key"`
	}{
		T: catType,
		K: key,
	}
	data, err := jsoniter.Marshal(b)
	if err != nil {
		return nil, fmt.Errorf("marshal action param: %w", err)
	}

	a := flight.Action{
		Type: "REQ_GET_CATALOG",
		Body: data,
	}
	dc, err := s.client.DoAction(ctx, &a)
	if err != nil {
		return nil, fmt.Errorf("do action: %w", err)
	}

	r, err := dc.Recv()
	if err != nil {
		return nil, fmt.Errorf("get result: %w", err)
	}

	return r.Body, nil
}

func (s Stonewave) GetDataType(ctx context.Context, datatype string) (*Datatype, error) {
	data, err := s.GetCatalog(ctx, "DATATYPE", datatype)
	if err != nil {
		return nil, fmt.Errorf("get datatype %s: %w", datatype, err)
	}

	var dt GetDataTypeResponse
	err = jsoniter.Unmarshal(data, &dt)
	if err != nil {
		return nil, fmt.Errorf("unmarshal datatype %s: %w", datatype, err)
	}

	if dt.StatusCode != 0 {
		return nil, fmt.Errorf("get datatype %s: statusCode=%d", datatype, dt.StatusCode)
	}

	return NewDatatypeForCatalog(datatype, dt.Data), nil
}

func NewDatatypeForCatalog(name string, attributes map[string]interface{}) *Datatype {
	metadata := make(map[string][]string)
	for k, v := range attributes {
		if k != "attributes" {
			continue
		}

		attrs, ok := v.(map[string]interface{})
		if !ok {
			continue
		}
		for ak, av := range attrs {
			switch tv := av.(type) {
			case string:
				metadata[ak] = []string{tv}
			case bool:
				metadata[ak] = []string{strconv.FormatBool(tv)}
			case []interface{}:
				metadata[ak] = make([]string, len(tv))
				for i := 0; i < len(tv); i++ {
					switch tt := tv[i].(type) {
					case string:
						metadata[ak][i] = tt
					case bool:
						metadata[ak][i] = strconv.FormatBool(tt)
					}
				}
			}
		}
	}

	datatype := NewDefaultDatatype(name)
	DatatypeDecoder.Decode(datatype, metadata)
	return datatype
}

func (s Stonewave) GetAllDataType(ctx context.Context) (map[string]Datatype, error) {
	data, err := s.GetCatalog(ctx, "DATATYPE", "")
	if err != nil {
		return nil, fmt.Errorf("get all datatype: %w", err)
	}

	var dt GetAllDataTypeResponse
	err = jsoniter.Unmarshal(data, &dt)
	if err != nil {
		return nil, fmt.Errorf("unmarshal all datatypes: %w", err)
	}

	if dt.StatusCode != 0 {
		return nil, fmt.Errorf("get all datatypes: statusCode=%d", dt.StatusCode)
	}

	dm := make(map[string]Datatype)
	for k, v := range dt.Data {
		if attrs, ok := v.(map[string]interface{}); ok {
			datatype := NewDatatypeForCatalog(k, attrs)
			if datatype != nil {
				dm[k] = *datatype
			}
		}
	}

	return dm, nil
}
