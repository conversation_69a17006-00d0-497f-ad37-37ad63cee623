/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package stonewave

import (
	_ "embed"
	"sync"

	"github.com/gorilla/schema"
	cmap "github.com/orcaman/concurrent-map"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

type Datatype struct {
	Name                    string
	IngestionTimeExtraction string   `schema:"ingestion_time_extraction"`
	Delimiter               string   `schema:"delimiter"`
	TimestampConfig         string   `schema:"timestamp_conf"`
	FirstLineFormat         string   `schema:"firstline_format"`
	IngestionTimeFieldNames []string `schema:"ingestion_time_field_names"`
	DiscardRawMessage       bool     `schema:"discard_raw_message"`
}

// DatatypeEqual compares if two datatype catalog are the same
func IsDatatypeEqual(d1 Datatype, d2 Datatype) bool {
	if d1.IngestionTimeExtraction != d2.IngestionTimeExtraction || d1.Delimiter != d2.Delimiter || d1.TimestampConfig != d2.TimestampConfig || d1.FirstLineFormat != d2.FirstLineFormat || d1.DiscardRawMessage != d2.DiscardRawMessage {
		return false
	}

	if len(d1.IngestionTimeFieldNames) != len(d2.IngestionTimeFieldNames) {
		return false
	}

	for i := 0; i < len(d1.IngestionTimeFieldNames); i++ {
		if d1.IngestionTimeFieldNames[i] != d2.IngestionTimeFieldNames[i] {
			return false
		}
	}

	return true
}

var DatatypeDecoder = schema.NewDecoder()

func NewDefaultDatatype(name string) *Datatype {
	return &Datatype{
		Name:                    name,
		IngestionTimeExtraction: "none",
		Delimiter:               ",",
		TimestampConfig:         "auto",
	}
}

//go:embed catalog.yaml
var defaultCatalog []byte

// type DatatypeListener func(name string, datatype *Datatype) error
type DatatypeListener interface {
	UpdateDatatype(*Datatype) error
	RemoveDatatype(name string) error
}

type DatatypeMap struct {
	m         cmap.ConcurrentMap
	listeners []DatatypeListener
	l         sync.Mutex
}

func NewDatatypeMap() *DatatypeMap {
	return &DatatypeMap{m: cmap.New()}
}

func NewDefaultDatatypeMap() *DatatypeMap {
	dm := NewDatatypeMap()

	var catalog map[string]map[string]interface{}
	yaml.Unmarshal(defaultCatalog, &catalog)
	// var dm DatatypeMap
	for k, v := range catalog {
		dm.SetDatatype(k, *NewDatatypeForCatalog(k, v))
	}
	return dm
}

func (dm *DatatypeMap) GetDatatype(name string) Datatype {
	if tmp, ok := dm.m.Get(name); ok {
		return tmp.(Datatype)
	}
	return *NewDefaultDatatype(name)
}

func (dm *DatatypeMap) SetDatatype(name string, dt Datatype) {
	defer func() {
		recover()
	}()

	old, ok := dm.m.Get(name)
	toUpdate := !ok || !IsDatatypeEqual(old.(Datatype), dt)
	if toUpdate {
		dm.m.Set(name, dt)
		for _, l := range dm.listeners {
			if err := l.UpdateDatatype(&dt); err != nil {
				zap.S().Infow("fail to call listener in update datatype", "listener", l)
			}
		}
	}
}

func (dm *DatatypeMap) RemoveDatatype(name string) {
	defer func() {
		recover()
	}()

	if dm.m.Has(name) {
		dm.m.Remove(name)
		for _, l := range dm.listeners {
			if err := l.RemoveDatatype(name); err != nil {
				zap.S().Infow("fail to call listener in update datatype", "listener", l)
			}
		}
	}
}

func (dm *DatatypeMap) Clear() {
	dm.m.Clear()
}

func (dm *DatatypeMap) IterBuffered() <-chan cmap.Tuple {
	return dm.m.IterBuffered()
}

// RegisterListener registers listener for DatatypeMap updates.
func (dm *DatatypeMap) RegisterListener(l DatatypeListener) {
	for item := range dm.m.IterBuffered() {
		dt, _ := item.Val.(Datatype)
		l.UpdateDatatype(&dt)
	}
	dm.l.Lock()
	dm.listeners = append(dm.listeners, l)
	dm.l.Unlock()
}

func (dm *DatatypeMap) UnRegisterListener(l DatatypeListener) {
	dm.l.Lock()
	defer dm.l.Unlock()
	for i, li := range dm.listeners {
		if li == l {
			dm.listeners = append(dm.listeners[:i], dm.listeners[i+1:]...)
			return
		}
	}
}
