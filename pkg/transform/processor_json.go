/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

type JSON struct {
	// Field used for json parsing
	Field string `option:"field,,_message"`
}

func JSONBuilder(o Options) (Processor, error) {
	j := new(JSON)
	if err := o.Apply(j); err != nil {
		return nil, err
	}

	return j, nil
}

func (j *JSON) Process(e *Event) (*Event, error) {
	source := e.GetStringField(j.Field)

	var data map[string]interface{}
	if err := jsonAPI.Unmarshal([]byte(source), &data); err != nil {
		return e, err
	}

	for k, v := range data {
		e.SetRealField(k, v)
	}

	return e, nil
}
