/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCSVEmitOption(t *testing.T) {
	t.Run("option delimiter is required", func(t *testing.T) {
		o := &Options{}
		e := new(CSVEmitter)
		err := o.Apply(e)
		assert.Error(t, err)
	})
}

func TestNewCSVEmitter(t *testing.T) {
	cases := []struct {
		name         string
		options      Options
		delimiter    rune
		delimiterLen int
	}{
		{
			name: "comma",
			options: Options{
				"delimiter": ",",
			},
			delimiter:    ',',
			delimiterLen: 1,
		},
		{
			name: "tab",
			options: Options{
				"delimiter": "\t",
			},
			delimiter:    '\t',
			delimiterLen: 1,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			e, err := NewCSVEmitter(strings.NewReader(""), tc.options)
			assert.NoError(t, err)

			ce, ok := e.(*CSVEmitter)
			assert.True(t, ok)
			assert.Equal(t, tc.delimiter, ce.delimiter)
			assert.Equal(t, tc.delimiterLen, ce.delimiterLen)
		})
	}
}

func TestCSVEmit(t *testing.T) {
	cases := []struct {
		name   string
		data   string
		events []Event
	}{
		{
			name: "basic",
			data: `a,b,c
			aa,bb,cc`,
			events: []Event{
				{
					Message: "a,b,c",
					Fields:  map[string]interface{}{},
				},
				{
					Message: "aa,bb,cc",
					Fields:  map[string]interface{}{},
				},
			},
		},
		{
			name: "invalid col count",
			data: `
			a,b,c
			d,e
			f,g,h,i
			j,k,l`,
			events: []Event{
				{
					Message: "a,b,c",
					Fields:  map[string]interface{}{},
				},
				{
					Message: "d,e",
					Fields:  map[string]interface{}{},
				},
				{
					Message: "f,g,h,i",
					Fields:  map[string]interface{}{},
				},
				{
					Message: "j,k,l",
					Fields:  map[string]interface{}{},
				},
			},
		},
		{
			name: "multiline",
			data: `a,b,"c
d"
			e,f,g`,
			events: []Event{
				{
					Message: "a,b,\"c\nd\"",
					Fields:  map[string]interface{}{},
				},
				{
					Message: "e,f,g",
					Fields:  map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			o := Options{"delimiter": ","}
			emitter, err := NewCSVEmitter(strings.NewReader(tc.data), o)
			assert.NoError(t, err)

			var emittedEvents []Event
			for {
				event, err := emitter.Emit()
				if err == io.EOF {
					break
				}
				emittedEvents = append(emittedEvents, *event)
			}

			assert.Equal(t, len(tc.events), len(emittedEvents))

			for i, e := range tc.events {
				assert.Equal(t, e.Message, emittedEvents[i].Message)
				assert.Equal(t, e.Fields, emittedEvents[i].Fields)
			}
		})
	}
}

func TestTSV(t *testing.T) {
	cases := []struct {
		name   string
		data   string
		events []Event
	}{
		{
			name: "basic",
			data: `
a	"b c"	c`,
			events: []Event{
				{
					Message: "a\t\"b c\"\tc",
					Fields:  map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			o := Options{"delimiter": "\t"}
			emitter, err := NewCSVEmitter(strings.NewReader(tc.data), o)
			assert.NoError(t, err)

			var emittedEvents []Event
			for {
				event, err := emitter.Emit()
				if err == io.EOF {
					break
				}
				emittedEvents = append(emittedEvents, *event)
			}

			assert.Equal(t, len(tc.events), len(emittedEvents))

			for i, e := range tc.events {
				assert.Equal(t, e.Message, emittedEvents[i].Message)
				assert.Equal(t, e.Fields, emittedEvents[i].Fields)
			}
		})
	}
}

// func TestFields(t *testing.T) {
// 	cases := []struct {
// 		name   string
// 		data   string
// 		fields []string
// 		events []Event
// 	}{
// 		{
// 			name: "basic",
// 			data: `
// 			a,b,c
// 			aa,bb,cc`,
// 			fields: []string{"field1", "field2", "field3"},
// 			events: []Event{
// 				{
// 					Message: "a,b,c",
// 					Fields: map[string]interface{}{
// 						"field1": "a",
// 						"field2": "b",
// 						"field3": "c",
// 					},
// 				},
// 				{
// 					Message: "aa,bb,cc",
// 					Fields: map[string]interface{}{
// 						"field1": "aa",
// 						"field2": "bb",
// 						"field3": "cc",
// 					},
// 				},
// 			},
// 		},
// 		{
// 			name: "less fields",
// 			data: `
// 			a,b,c
// 			aa,bb,cc`,
// 			fields: []string{"field1","field2"},
// 			events: []Event{
// 				{
// 					Message: "a,b,c",
// 					Fields:  map[string]interface{}{},
// 				},
// 				{
// 					Message: "aa,bb,cc",
// 					Fields:  map[string]interface{}{},
// 				},
// 			},
// 		},
// 		{
// 			name: "more fields",
// 			data: `
// 			a,b,c
// 			aa,bb,cc`,
// 			fields: []string{"field1", "field2", "field3", "field4"},
// 			events: []Event{
// 				{
// 					Message: "a,b,c",
// 					Fields:  map[string]interface{}{},
// 				},
// 				{
// 					Message: "aa,bb,cc",
// 					Fields:  map[string]interface{}{},
// 				},
// 			},
// 		},
// 	}

// 	for _, tc := range cases {
// 		t.Run(tc.name, func(t *testing.T) {
// 			o := Options{"delimiter": ",", "ingestion_time_field_names": tc.fields}
// 			emitter, err := NewCSVEmitter(strings.NewReader(tc.data), o)
// 			assert.NoError(t, err)

// 			var emittedEvents []Event
// 			for {
// 				event, err := emitter.Emit()
// 				if err == io.EOF {
// 					break
// 				}
// 				emittedEvents = append(emittedEvents, *event)
// 			}

// 			assert.Equal(t, len(tc.events), len(emittedEvents))

// 			for i, e := range tc.events {
// 				assert.Equal(t, e.Message, emittedEvents[i].Message)
// 				assert.Equal(t, e.Fields, emittedEvents[i].Fields)
// 			}
// 		})
// 	}
// }
