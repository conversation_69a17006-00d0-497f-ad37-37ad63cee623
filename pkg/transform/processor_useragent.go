/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"github.com/ua-parser/uap-go/uaparser"
)

type UserAgent struct {
	// Field containing the user agent string.
	Field string `option:"field,required"`
	// TragetField that will be filled with the user agent details.
	TargetField string `option:"target_field,,user_agent"`

	parser *uaparser.Parser

	// target field names
	fName    string
	fOS      string
	fOSName  string
	fOSMajor string
	fOSMinor string
	fDevice  string
	fMajor   string
	fMinor   string
	fPatch   string
	fBuild   string
}

func UserAgentBuilder(o Options) (Processor, error) {
	ua := new(UserAgent)
	err := o.Apply(ua)
	if err != nil {
		return nil, err
	}

	ua.parser, err = uaparser.NewFromBytes(uaparser.DefinitionYaml)
	if err != nil {
		return nil, err
	}

	var target string
	if ua.TargetField != "" {
		target = ua.TargetField + "."
	}
	ua.fName = target + "name"
	ua.fOS = target + "os"
	ua.fOSName = target + "os_name"
	ua.fOSMajor = target + "os_major"
	ua.fOSMinor = target + "os_minor"
	ua.fDevice = target + "device"
	ua.fMajor = target + "major"
	ua.fMinor = target + "minor"
	ua.fPatch = target + "patch"
	ua.fBuild = target + "build"

	return ua, nil
}

func (ua UserAgent) Process(e *Event) (*Event, error) {
	source := e.GetStringField(ua.Field)
	if source == "" {
		return e, nil
	}

	client := ua.parser.Parse(source)
	if client.Os != nil {
		e.SetField(ua.fOS, client.Os.Family)
		if client.Os.Family != "" {
			e.SetField(ua.fOSName, client.Os.Family)
		}
		if client.Os.Major != "" {
			e.SetField(ua.fOSMajor, client.Os.Major)
		}
		if client.Os.Minor != "" {
			e.SetField(ua.fOSMinor, client.Os.Minor)
		}
	}
	if client.Device != nil {
		e.SetField(ua.fDevice, client.Device.Family)
	}
	if client.UserAgent != nil {
		e.SetField(ua.fName, client.UserAgent.Family)
		if client.UserAgent.Major != "" {
			e.SetField(ua.fMajor, client.UserAgent.Major)
		}
		if client.UserAgent.Minor != "" {
			e.SetField(ua.fMinor, client.UserAgent.Minor)
		}
		if client.UserAgent.Patch != "" {
			e.SetField(ua.fPatch, client.UserAgent.Patch)
		}
	}

	return e, nil
}
