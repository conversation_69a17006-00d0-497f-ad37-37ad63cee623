/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewGrok(t *testing.T) {
	t.Run("require patterns", func(t *testing.T) {
		o := Options{}
		g, err := GrokBuilder(o)
		assert.Erro<PERSON>(t, err)
		assert.Nil(t, g)
	})
}

func TestGrokProcess(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		source string
		fields map[string]interface{}
	}{
		{
			name: "apache log",
			option: Options{
				"patterns": []string{"%{COMMONAPACHELOG}"},
			},
			source: `127.0.0.1 - - [23/Apr/2014:22:58:32 +0200] "GET /index.php HTTP/1.1" 404 207`,
			fields: map[string]interface{}{
				"auth":        "-",
				"bytes":       "207",
				"clientip":    "127.0.0.1",
				"httpversion": "1.1",
				"ident":       "-",
				"request":     "/index.php",
				"response":    "404",
				"timestamp":   "23/Apr/2014:22:58:32 +0200",
				"verb":        "GET",
			},
		},
		{
			name: "apache log",
			option: Options{
				"patterns":            []string{"^%{SYSLOGTIMESTAMP:time} %{SYSLOGHOST:host} %{GREEDYMULTILINE:message}$"},
				"pattern_definitions": map[string]string{"GREEDYMULTILINE": "(.|\n)*"},
			},
			source: `Jul 27 11:27:40 Jians-MacBook-Pro.local 07-27-2021 11:27:40.811 +0800 INFO  Metrics - group=deploy-connections, nCurrent=2`,
			fields: map[string]interface{}{
				"time":    "Jul 27 11:27:40",
				"host":    "Jians-MacBook-Pro.local",
				"message": "07-27-2021 11:27:40.811 +0800 INFO  Metrics - group=deploy-connections, nCurrent=2",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			g, err := GrokBuilder(tc.option)
			assert.NoError(t, err)

			e := NewEvent()
			e.Message = tc.source

			e, err = g.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.fields, e.Fields)
		})
	}
}
