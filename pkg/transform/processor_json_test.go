/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJSONProcess(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		source string
		fields map[string]interface{}
	}{
		{
			name:   "simple",
			source: `{"a": "test", "b": 1, "c": true}`,
			fields: map[string]interface{}{
				"a": "test",
				"b": json.Number("1"),
				"c": true,
			},
		},
		{
			name:   "varies field types",
			source: `{"str1": "string", "str2": "0", "str3": "汉", "str4": "\\", "str5": "'", "str6": "\"", "str7": "*", "str8": " ", "str9": "null", "str10": "Null", "str11": null, "number1": 0, "number2": 100000000000000000000000, "number3": -100000000000000000000000, "number4": 1e-24, "number5": -1e-24, "number6": 0.0, "bool1": true, "bool2": "true", "bool3": false, "bool4": "False"}`,
			fields: map[string]interface{}{
				"str1":    "string",
				"str2":    "0",
				"str3":    "汉",
				"str4":    "\\",
				"str5":    "'",
				"str6":    "\"",
				"str7":    "*",
				"str8":    " ",
				"str9":    "null",
				"str10":   "Null",
				"str11":   nil,
				"number1": json.Number("0"),
				"number2": json.Number("100000000000000000000000"),
				"number3": json.Number("-100000000000000000000000"),
				"number4": json.Number("1e-24"),
				"number5": json.Number("-1e-24"),
				"number6": json.Number("0.0"),
				"bool1":   true,
				"bool2":   "true",
				"bool3":   false,
				"bool4":   "False",
			},
		},
		{
			name:   "int64 range",
			source: `{"max_int64": 9223372036854775807, "min_int64": -9223372036854775808, "gt_int64": 9223372036854775808, "lt_int64": -9223372036854775809}`,
			fields: map[string]interface{}{
				"max_int64": json.Number("9223372036854775807"),
				"min_int64": json.Number("-9223372036854775808"),
				"gt_int64":  json.Number("9223372036854775808"),
				"lt_int64":  json.Number("-9223372036854775809"),
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			j, err := JSONBuilder(tc.option)
			assert.NoError(t, err)

			e := NewEvent()
			e.Message = tc.source

			e, err = j.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.fields, e.Fields)
		})
	}
}
