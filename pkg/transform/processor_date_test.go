/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestLocation(t *testing.T) {
	testCases := []struct {
		name     string
		o        Options
		location *time.Location
	}{
		{
			name:     "default",
			o:        Options{"field": "_message"},
			location: time.FixedZone("CST", 8*3600),
		},
		{
			name:     "invalid location",
			o:        Options{"field": "_message", "location": "Jiuzhou/KunlunShan"},
			location: time.Local,
		},
		{
			name:     "set location",
			o:        Options{"field": "_message", "location": "America/Panama"},
			location: time.FixedZone("EST", -5*3600),
		},
	}

	now := time.Now()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			d, err := DateBuilder(tc.o)
			assert.NoError(t, err)
			assert.Equal(t, now.In(tc.location).String(), now.In(d.(*Date).loc).String())
		})
	}
}

func TestDateProcessor(t *testing.T) {
	cases := []struct {
		name     string
		location string
		input    string
		want     time.Time
	}{
		{
			name:     "UTC",
			location: "UTC",
			input:    "2021-07-27 15:21:00",
			want:     time.Date(2021, 07, 27, 15, 21, 0, 0, time.UTC),
		},
		{
			name:     "Shanghai Timezone",
			location: "Asia/Shanghai",
			input:    "2021-07-27 15:21:00",
			want:     time.Date(2021, 07, 27, 7, 21, 0, 0, time.UTC),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			o := Options{"field": "_message"}
			if tc.location != "" {
				o.Set("location", tc.location)
			}
			d, err := DateBuilder(o)
			assert.NoError(t, err)

			e := NewEvent()
			e.Message = tc.input

			e, err = d.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.want.In(time.UTC), e.Time.In(time.UTC))
		})
	}
}
