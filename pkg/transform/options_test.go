/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
)

func TestApplyOption(t *testing.T) {
	type testNode struct {
		StringField      string            `option:"str_field"`
		BoolField        bool              `option:"bool_field"`
		StringSliceField []string          `option:"string_slice_field"`
		StringMapField   map[string]string `option:"string_map_field"`
	}

	node := new(testNode)

	cases := []struct {
		name      string
		nodeKey   string
		optionKey string
		value     interface{}
	}{
		{
			name:      "string option",
			nodeKey:   "StringField",
			optionKey: "str_field",
			value:     "test-string",
		},
		{
			name:      "bool option",
			nodeKey:   "BoolField",
			optionKey: "bool_field",
			value:     true,
		},
		{
			name:      "string slice option",
			nodeKey:   "String<PERSON>lice<PERSON>ield",
			optionKey: "string_slice_field",
			value:     []string{"a", "b", "c"},
		},
		{
			name:      "string map option",
			nodeKey:   "StringMapField",
			optionKey: "string_map_field",
			value:     map[string]string{"a": "1", "b": "2", "c": "3"},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			option := make(Options)
			option.Set(tc.optionKey, tc.value)
			err := option.Apply(node)
			assert.NoError(t, err)
			assert.Equal(t, tc.value, reflect.ValueOf(node).Elem().FieldByName(tc.nodeKey).Interface())
		})
	}
}

func TestLoadYAMLConfig(t *testing.T) {
	config := `
grok:
  field: _message
  patterns:
    - (%{NGINX_HOST} )?"?(?:%{NGINX_ADDRESS_LIST:nginx.access.remote_ip_list}|%{NOTSPACE:source.address})
      - (-|%{DATA:user.name}) \[%{HTTPDATE:nginx.access.time}\] "%{DATA:nginx.access.info}"
      %{NUMBER:http.response.status_code} %{NUMBER:http.response.body.bytes}
      "(-|%{DATA:http.request.referrer})" "(-|%{DATA:user_agent.original})"
  pattern_definitions:
    NGINX_HOST: (?:%{IP:destination.ip}|%{NGINX_NOTSEPARATOR:destination.domain})(:%{NUMBER:destination.port})?
    NGINX_NOTSEPARATOR: "[^\t ,:]+"
    NGINX_ADDRESS_LIST: (?:%{IP}|%{WORD})("?,?\s*(?:%{IP}|%{WORD}))*
`
	opt := make(Options)
	err := yaml.Unmarshal([]byte(config), &opt)
	assert.NoError(t, err)

	grok_opt := opt.Get("grok").(Options)
	g := new(Grok)
	err = grok_opt.Apply(g)
	assert.NoError(t, err)
	assert.Equal(t, "_message", g.Field)
	assert.Equal(t, 1, len(g.Patterns))
	assert.Equal(t, 3, len(g.PatternDefinitions))
	assert.Contains(t, g.PatternDefinitions, "NGINX_HOST")
	assert.Contains(t, g.PatternDefinitions, "NGINX_NOTSEPARATOR")
	assert.Contains(t, g.PatternDefinitions, "NGINX_ADDRESS_LIST")
	assert.Equal(t, "[^\t ,:]+", g.PatternDefinitions["NGINX_NOTSEPARATOR"])
}

func TestCloneOptions(t *testing.T) {
	obj := struct {
		A string `option:"a"`
		B string `option:"b"`
	}{
		A: "abc",
	}
	opt := CloneOptions(&obj)
	assert.Equal(t, "abc", opt["a"])
	assert.Equal(t, "", opt["b"])
}
