/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"regexp"
	"strings"
)

type Regexp struct {
	// Field used for regex expression parsing
	Field string `option:"field,,_message"`

	// Patterns are the regex expression used to extract fields
	Patterns []string `option:"patterns,required"`

	res []*regexp.Regexp
	// fields []string
}

func RegexpBuilder(o Options) (Processor, error) {
	reg := new(Regexp)

	err := o.Apply(reg)
	if err != nil {
		return nil, err
	}

	reg.res = make([]*regexp.Regexp, 0, len(reg.Patterns))
	for _, p := range reg.Patterns {
		r, err := regexp.Compile(p)
		if err == nil {
			reg.res = append(reg.res, r)
		}
	}

	return reg, nil
}

func (reg *Regexp) Process(e *Event) (*Event, error) {
	source := e.Get<PERSON>(reg.Field)

	for _, re := range reg.res {
		values := re.FindStringSubmatch(source)
		if values != nil {
			fields := re.SubexpNames()
			for i := 1; i < len(fields); i++ {
				v := strings.TrimSpace(values[i])
				if len(v) > 0 {
					e.SetRealField(fields[i], v)
				}
			}
			// return with the first matched regex
			return e, nil
		}
	}

	return e, nil
}
