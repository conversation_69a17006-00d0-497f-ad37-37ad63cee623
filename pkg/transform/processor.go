/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"go.uber.org/zap"
)

type Processor interface {
	Process(e *Event) (*Event, error)
}

type Remove struct {
	// Fields to be removed.
	Field string `option:"field,required"`
}

func RemoveBuilder(o Options) (Processor, error) {
	r := new(Remove)
	err := o.Apply(r)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (r Remove) Process(e *Event) (*Event, error) {
	e.<PERSON>(r.Field)
	return e, nil
}

func runProcessor(proc Processor, in <-chan *Event, out chan<- *Event) {
	defer close(out)
	runProcessorNoCloser(proc, in, out)
}

func runProcessorNoCloser(proc Processor, in <-chan *Event, out chan<- *Event) {
	var err error
	for event := range in {
		if event != nil {
			event, err = proc.Process(event)
			if err != nil {
				zap.S().Debugw("process error", "error", err)
			}
		}

		out <- event
	}
}
