/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFlattenProcess(t *testing.T) {
	cases := []struct {
		name   string
		fields map[string]interface{}
		want   map[string]interface{}
	}{
		{
			name: "simple",
			fields: map[string]interface{}{
				"a": "test",
				"b": float64(1),
				"c": true,
			},
			want: map[string]interface{}{
				"a": "test",
				"b": float64(1),
				"c": true,
			},
		},
		{
			name: "array",
			fields: map[string]interface{}{
				"a": []interface{}{1, 2, 3},
			},
			want: map[string]interface{}{
				"a[]": []interface{}{1, 2, 3},
			},
		},
		{
			name: "map",
			fields: map[string]interface{}{
				"a": map[string]interface{}{"a": 1, "b": 2},
			},
			want: map[string]interface{}{
				"a.a": 1,
				"a.b": 2,
			},
		},
		{
			name: "nested fields",
			fields: map[string]interface{}{
				"a": map[string]interface{}{
					"b": 1,
					"c": true,
					"d": "x",
				},
			},
			want: map[string]interface{}{
				"a.b": 1,
				"a.c": true,
				"a.d": "x",
			},
		},
		{
			name: "array fields",
			fields: map[string]interface{}{
				"a": []interface{}{1},
				"b": []interface{}{2, 3},
				"c": map[string]interface{}{
					"d": []interface{}{"4", "5"},
					"e": []interface{}{
						map[string]interface{}{
							"f": "x",
						},
					},
				},
			},
			want: map[string]interface{}{
				"a[]":     []interface{}{1},
				"b[]":     []interface{}{2, 3},
				"c.d[]":   []interface{}{"4", "5"},
				"c.e[].f": []interface{}{"x"},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			f, err := FlattenBuilder(nil)
			assert.NoError(t, err)

			e := NewEvent()
			e.Fields = tc.fields

			e, err = f.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.want, e.Fields)
		})
	}
}
