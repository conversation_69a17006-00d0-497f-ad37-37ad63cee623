/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/araddon/dateparse"
	jsoniter "github.com/json-iterator/go"
)

var TimeZero = time.Unix(0, 0)

const DefaultDatatype = "plain_text"
const DefaultEventSet = "main"

type Event struct {
	Time     time.Time
	Message  string
	Datatype string
	EventSet string
	Fields   map[string]interface{}
	Metadata interface{}
}

func NewEvent() *Event {
	return &Event{
		Time:     TimeZero,
		Datatype: DefaultDatatype,
		EventSet: DefaultEventSet,
		Fields:   make(map[string]interface{}),
	}
}

func (e *Event) String() string {
	data, err := jsoniter.MarshalIndent(e, "", "  ")
	if err != nil {
		return err.Error()
	}
	return string(data)
}

func (e *Event) ToPreviewBytes() []byte {
	msg := make(map[string]interface{}, len(e.Fields)+3)
	msg["_time"] = e.Time.UnixNano() / 1000
	msg["_message"] = e.Message
	msg["_datatype"] = e.Datatype

	for k, v := range e.Fields {
		msg[k] = v
	}

	data, err := jsoniter.Marshal(msg)
	if err != nil {
		return nil
	}
	return data
}

func (e *Event) ToPreviewString() string {
	data := e.ToPreviewBytes()
	return *(*string)(unsafe.Pointer(&data))
}

func getStringFieldByPath(m map[string]interface{}, path string) (s string) {
	is, ok := m[path]
	if ok {
		s, _ = is.(string)
		return s
	}
	if i := strings.Index(path, "."); i >= 0 {
		im, ok := m[path[:i]]
		if !ok {
			return
		}
		sm, ok := im.(map[string]interface{})
		if !ok {
			return
		}
		return getStringFieldByPath(sm, path[i+1:])
	} else {
		is, ok := m[path]
		if ok {
			s, _ = is.(string)
		}
		return
	}
}

func (e *Event) GetStringField(name string) (s string) {
	switch name {
	case "_message":
		return e.Message
	case "_datatype":
		return e.Datatype
	case "_event_set":
		return e.EventSet
	}

	return getStringFieldByPath(e.Fields, name)
}

// SetField set value to event. If name is reserved internal key, it sets corresponding Event field.
// Otherwise it sets the value in Event.Fields map
func (e *Event) SetField(name string, value interface{}) {
	if len(name) == 0 {
		return
	}

	if e.Fields == nil {
		e.Fields = make(map[string]interface{})
	}

	switch name {
	case "_message":
		e.Message, _ = value.(string)
		return
	case "_time":
		// parse json.Number timestamp
		if number, ok := value.(json.Number); ok {
			var pe error
			value, pe = number.Int64()
			if pe != nil {
				value, _ = number.Float64()
			}
		}

		switch vt := value.(type) {
		case int32:
			t64 := int64(vt)
			e.Time = time.Unix(t64/1000000, (t64%1000000)*1000)
		case int64:
			e.Time = time.Unix(vt/1000000, (vt%1000000)*1000)
		case float64:
			sec := int64(vt)
			nsec := int64((vt - float64(sec)) * 1000000000)
			e.Time = time.Unix(sec, nsec)
		case string:
			if t, err := dateparse.ParseAny(vt); err == nil {
				e.Time = t
			}
		default:
			e.Fields["_time$1"] = vt
		}
		return
	case "_datatype":
		e.Datatype, _ = value.(string)
		return
	case "_event_set":
		e.EventSet, _ = value.(string)
		return
	}

	// setFieldByPath(e.Fields, name, value)
	e.Fields[name] = value
}

// SetRealField set event's Fields map. It append "$1" if the field name conflicts with internal fields, such as _time
func (e *Event) SetRealField(name string, value interface{}) {
	if name == "_message" || name == "_time" || name == "_datatype" || name == "_event_set" || name == "_host" || name == "_source" {
		name = name + "$1"
	}
	e.SetField(name, value)
}

func (e *Event) DeleteField(name string) {
	switch name {
	case "_message":
		e.Message = ""
	case "_time":
		e.Time = TimeZero
	case "_datatype":
		e.Datatype = ""
	case "_event_set":
		e.EventSet = ""
	default:
		delete(e.Fields, name)
	}
}

var trueValue = []byte("true")
var falseValue = []byte("false")

const bufInitSize = 16

func EncodeValue(v interface{}) []byte {
	switch vt := v.(type) {
	case string:
		qs := strconv.Quote(vt)
		return *(*[]byte)(unsafe.Pointer(&qs))
	case bool:
		if vt {
			return trueValue
		} else {
			return falseValue
		}
	case json.Number:
		// if number is integer
		if strings.IndexByte(string(vt), '.') == -1 {
			if _, err := vt.Int64(); err == nil {
				return *(*[]byte)(unsafe.Pointer(&vt))
			}
			buf := make([]byte, 0, len(vt)+2)
			return strconv.AppendQuote(buf, string(vt))
		}

		// if number is float
		if _, err := vt.Float64(); err == nil {
			return *(*[]byte)(unsafe.Pointer(&vt))
		}
		buf := make([]byte, 0, len(vt)+2)
		return strconv.AppendQuote(buf, string(vt))
	case []interface{}:
		buf := make([]byte, 0, bufInitSize)
		buf = append(buf, '[')
		for i, item := range vt {
			if i != 0 {
				buf = append(buf, ',')
			}
			buf = append(buf, EncodeValue(item)...)
		}
		buf = append(buf, ']')
		return buf
	case map[string]interface{}:
		buf := make([]byte, 0, bufInitSize)
		buf = append(buf, '{')
		for mk, mv := range vt {
			if len(buf) > 1 {
				buf = append(buf, ',')
			}
			buf = strconv.AppendQuote(buf, mk)
			buf = append(buf, ':')
			buf = append(buf, EncodeValue(mv)...)
		}
		buf = append(buf, '}')
		return buf
	default:
		data, _ := jsoniter.Marshal(vt)
		return data
	}
}
