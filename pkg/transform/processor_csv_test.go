/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCSVProcess(t *testing.T) {
	cases := []struct {
		name    string
		delimit string
		headers []string
		source  string
		fields  map[string]interface{}
	}{
		{
			name:    "simple",
			delimit: ",",
			headers: []string{"field1", "field2", "field3"},
			source:  `a,b,c`,
			fields: map[string]interface{}{
				"field1": "a",
				"field2": "b",
				"field3": "c",
			},
		},
		{
			name:    "more fields in row",
			delimit: ",",
			headers: []string{"field1", "field2", "field3"},
			source:  `a,b,c,d,e`,
			fields: map[string]interface{}{
				"field1":        "a",
				"field2":        "b",
				"field3":        "c",
				"EXTRA_FIELD_4": "d",
				"EXTRA_FIELD_5": "e",
			},
		},
		{
			name:    "fields with tab delimiter",
			delimit: "	",
			headers: []string{"field1", "field2", "field3"},
			source:  `a	b	c`,
			fields: map[string]interface{}{
				"field1": "a",
				"field2": "b",
				"field3": "c",
			},
		},
		{
			name:    "fields with tab delimiter and double quoted value with quotes inside",
			delimit: "	",
			headers: []string{"field1", "field2", "field3"},
			source:  `a	"b1 ""' b2"	c`,
			fields: map[string]interface{}{
				"field1": "a",
				"field2": "b1 \"' b2",
				"field3": "c",
			},
		},
		{
			name:    "fields with tab delimiter and quote in value",
			delimit: "	",
			headers: []string{"field1", "field2", "field3"},
			source:  `a	b1 "' b2	c`,
			fields: map[string]interface{}{
				"field1": "a",
				"field2": "b1 \"' b2",
				"field3": "c",
			},
		},
		{
			name:    "less fields in row",
			delimit: ",",
			headers: []string{"field1", "field2", "field3"},
			source:  `a`,
			fields: map[string]interface{}{
				"field1": "a",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			o := Options{
				"delimiter":                  tc.delimit,
				"ingestion_time_field_names": tc.headers,
			}
			c, err := CSVBuilder(o)
			assert.NoError(t, err)

			e := NewEvent()
			e.Message = tc.source

			e, err = c.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.fields, e.Fields)
		})
	}
}
