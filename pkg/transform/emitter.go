/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"errors"
	"io"
	"nile/pkg/stonewave"

	"go.uber.org/zap"
)

var ErrUnrecoverable = errors.New("uncoverable error in emit")

type Emitter interface {
	// Emit generate a new event or non-nil error but not both.
	// It returns nil, io.EOF if no more data available
	Emit() (*Event, error)
}

func NewEmitterForDatatype(datatype *stonewave.Datatype, r io.Reader) (Emitter, error) {
	switch datatype.IngestionTimeExtraction {
	case "json":
		zap.S().Debugw("create emitter", "type", "json", "datatype", datatype)
		return NewJSONEmitter(r, nil)
	case "csv":
		opt := Options{
			"delimiter": datatype.Delimiter,
		}
		zap.S().Debugw("create emitter", "type", "csv", "datatype", datatype)
		return NewCSVEmitter(r, opt)
	}

	if datatype.Name == "syslog" {
		opt := Options{
			"firstline_format": "^\\S",
		}
		zap.S().Debugw("create emitter", "type", "syslog_multiline", "datatype", datatype)
		return NewEventBreaker(r, opt)
	}

	if datatype.Name == "win_event_log" {
		zap.S().Debugw("create emitter", "type", "json", "datatype", datatype)
		return NewJSONEmitter(r, nil)
	}

	opt := Options{
		"firstline_format": datatype.FirstLineFormat,
	}
	zap.S().Debugw("create emitter", "type", "normal", "datatype", datatype)
	return NewEventBreaker(r, opt)
}
