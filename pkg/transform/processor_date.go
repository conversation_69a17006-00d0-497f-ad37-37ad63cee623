/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"time"

	"github.com/araddon/dateparse"
)

type Date struct {
	// Field to get the date from.
	Field string `option:"field,required"`
	// Location of the default timezone
	Location string `option:"location,,Asia/Shanghai"`

	loc *time.Location
}

func DateBuilder(o Options) (Processor, error) {
	d := new(Date)
	err := o.Apply(d)
	if err != nil {
		return nil, err
	}

	loc, err := time.LoadLocation(d.Location)
	if err == nil {
		d.loc = loc
	} else {
		d.loc = time.Local
	}

	return d, nil
}

func (d Date) Process(e *Event) (*Event, error) {
	t, err := dateparse.ParseIn(e.GetStringField(d.Field), d.loc)

	// do not set timestamp when parsing failed
	if err != nil {
		return e, nil
	}

	// if time do not have year
	if t.Year() == 0 {
		now := time.Now()
		year := now.Year()
		t = t.AddDate(year, 0, 0)
		// process the now time year is event year + 1
		// when event created just before the new year
		if t.After(now.AddDate(0, 1, 0)) {
			t.AddDate(-1, 0, 0)
		}
	}
	e.Time = t

	return e, nil
}
