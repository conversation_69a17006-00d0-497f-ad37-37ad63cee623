/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"regexp"
)

type EventBreaker struct {
	FirstlineFormat string `option:"firstline_format"`
	MaxLines        int    `option:"max_lines,,256"`

	r           *bufio.Reader
	firstRegexp *regexp.Regexp
	lines       [][]byte
}

func NewEventBreaker(r io.Reader, o Options) (Emitter, error) {
	eb := new(EventBreaker)
	err := o.Apply(eb)
	if err != nil {
		return nil, err
	}

	if eb.FirstlineFormat != "" {
		firstRegexp, err := regexp.Compile(eb.FirstlineFormat)
		if err == nil {
			eb.firstRegexp = firstRegexp
		}
	}

	if eb.MaxLines < 1 {
		eb.MaxLines = 1
	}

	eb.r = bufio.NewReader(r)

	return eb, nil
}

func (eb *EventBreaker) Emit() (*Event, error) {
	for {
		line, err := eb.r.ReadBytes('\n')

		if err != nil && err != io.EOF {
			return nil, fmt.Errorf("error in read line: %w", err)
		}

		if eb.firstRegexp == nil {
			line = bytes.TrimSpace(line)
			if len(line) > 0 {
				event := NewEvent()
				event.Message = string(line)
				return event, nil
			}
		} else {
			match := eb.firstRegexp.Match(line)
			if match && len(eb.lines) > 0 {
			// if (match || len(eb.lines) == eb.MaxLines) && len(eb.lines) > 0 {
				event := eb.genEvent()
				eb.lines = eb.lines[:0]
				if len(bytes.TrimSpace(line)) > 0 {
					eb.lines = append(eb.lines, line)
				}
				return event, nil
			}
			if len(bytes.TrimSpace(line)) > 0 {
				eb.lines = append(eb.lines, line)
			}
		}

		if err == io.EOF {
			if len(eb.lines) > 0 {
				event := eb.genEvent()
				eb.lines = eb.lines[:0]
				return event, nil
			}
			return nil, io.EOF
		}
	}
}

func (eb *EventBreaker) genEvent() *Event {
	var size int
	for i := range eb.lines {
		size += len(eb.lines[i])
	}
	data := make([]byte, 0, size)
	for i := range eb.lines {
		data = append(data, eb.lines[i]...)
	}
	event := NewEvent()
	event.Message = string(bytes.TrimSpace(data))
	return event
}
