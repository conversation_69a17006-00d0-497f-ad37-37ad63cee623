/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"encoding/json"
	"nile/pkg/stonewave"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestLoadDefaultWorkflow(t *testing.T) {
	datatypes := []string{
		"nginx__access_log",
		"apache__access_log",
		"syslog",
	}
	data := defaultWorkflow
	config, err := LoadNodesConfigFromYAML(data)
	assert.NoError(t, err)

	for _, dt := range datatypes {
		_, exists := config[dt]
		assert.True(t, exists, "datatype '%s' workflow not defined", dt)
	}
}

func TestNgixAccessLog(t *testing.T) {
	// test cases
	cases := []struct {
		name   string
		data   string
		time   string
		fields map[string]string
	}{
		{
			name: "ngix access log",
			data: `************ - - [16/Dec/2015:06:27:51 +0100] "GET /index.php?option=com_phocagallery&view=category&id=1&Itemid=53 HTTP/1.1" 200 32583 "-" "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)" "-"`,
			time: "16/Dec/2015:06:27:51 +0100",
			fields: map[string]string{
				"method": "GET",
				"remote": "************",
				// "http.request.method":         "GET",
				// "http.response.body.bytes":    "32583",
				// "http.response.status_code":   "200",
				// "http.version":                "1.1",
				// "nginx.access.remote_ip_list": "************",
				// "user_agent.name":             "bingbot",
			},
		},
		{
			name: "ngix access log with referer",
			data: `*********** - - [16/Dec/2015:06:30:08 +0100] "POST /administrator/index.php HTTP/1.1" 200 4494 "http://almhuette-raith.at/administrator/" "Mozilla/5.0 (Windows NT 6.0; rv:34.0) Gecko/20100101 Firefox/34.0" "-"`,
			time: "16/Dec/2015:06:30:08 +0100",
			fields: map[string]string{
				"method": "POST",
				"remote": "***********",
				// "http.request.method":         "POST",
				// "http.response.body.bytes":    "4494",
				// "http.response.status_code":   "200",
				// "http.version":                "1.1",
				// "nginx.access.remote_ip_list": "***********",
				// "http.request.referrer":       "http://almhuette-raith.at/administrator/",
			},
		},
		{
			name: "timestamp format - yyyy/mm/dd",
			data: `************* - - [2021/01/11 10:44:52.184 +0800] "POST /list HTTP/1.0" 200 5022 "https://www.stokes-scott.biz/category/" "Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10_8_8; rv:********) Gecko/2020-12-31 17:30:23 Firefox/3.6.14"`,
			time: "11/Jan/2021:10:44:52.184 +0800",
			fields: map[string]string{
				"method": "POST",
				"remote": "*************",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			event := NewEvent()
			event.Message = tc.data

			wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: "nginx__access_log"})
			wf.Start()
			wf.Input() <- event
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}
			assert.Equal(t, 1, len(result))
			//extracted timestamp
			accessTime, _ := time.Parse("02/Jan/2006:15:04:05 -0700", tc.time)
			assert.Equal(t, accessTime.UTC(), result[0].Time.UTC())

			// check fields
			for k, v := range tc.fields {
				assert.Equal(t, v, result[0].Fields[k], "check '%s' field value", k)
			}
		})
	}
}

func BenchmarkNgixAccessLog1(b *testing.B) {
	wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: "nginx__access_log"})

	for n := 0; n < b.N; n++ {
		wf.Start()

		done := make(chan struct{})
		go func() {
			defer close(done)
			for range wf.Output() {
			}
		}()

		for i := 0; i < 1000000; i++ {
			event := NewEvent()
			event.Message = `************ - - [16/Dec/2015:06:27:51 +0100] "GET /index.php?option=com_phocagallery&view=category&id=1&Itemid=53 HTTP/1.1" 200 32583 "-" "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)" "-"`
			wf.Input() <- event
		}

		wf.AsyncClose()
		<-done
	}
}

func TestApacheAccessLog(t *testing.T) {
	// test cases
	cases := []struct {
		name   string
		data   string
		time   string
		fields map[string]string
	}{
		{
			name: "apache access log",
			data: `::1 - - [26/Dec/2016:16:16:29 +0200] "GET /favicon.ico HTTP/1.1" 404 209`,
			time: "26/Dec/2016:16:16:29 +0200",
			fields: map[string]string{
				"method": "GET",
				"host":   "::1",
				// "http.request.method":       "GET",
				// "http.response.body.bytes":  "209",
				// "http.response.status_code": "404",
				// "http.version":              "1.1",
				// "source.ip":                 "::1",
				// "url.original":              "/favicon.ico",
			},
		},
		{
			name: "apache access log with user agent",
			data: `************ - - [26/Dec/2016:16:22:13 +0000] "GET /hello HTTP/1.1" 404 499 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.12; rv:50.0) Gecko/20100101 Firefox/50.0"`,
			time: "26/Dec/2016:16:22:13 +0000",
			fields: map[string]string{
				"method": "GET",
				"host":   "************",
				"path":   "/hello",
				"size":   "499",
				"agent":  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.12; rv:50.0) Gecko/20100101 Firefox/50.0",
				// "http.request.method":       "GET",
				// "http.response.body.bytes":  "499",
				// "http.response.status_code": "404",
				// "http.version":              "1.1",
				// "source.ip":                 "************",
				// "url.original":              "/hello",
				// "user_agent.name":           "Firefox",
			},
		},
		{
			name: "apache access log with referer",
			data: `************ - - [26/Dec/2016:16:22:00 +0000] "GET /favicon.ico HTTP/1.1" 404 504 "http://*************/" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.98 Safari/537.36"`,
			time: "26/Dec/2016:16:22:00 +0000",
			fields: map[string]string{
				"method": "GET",
				"host":   "************",
				// "http.request.method":       "GET",
				// "http.request.referrer":     "http://*************/",
				// "http.response.body.bytes":  "504",
				// "http.response.status_code": "404",
				// "http.version":              "1.1",
				// "source.ip":                 "************",
				// "url.original":              "/favicon.ico",
				// "user_agent.name":           "Chrome",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			event := NewEvent()
			event.Message = tc.data

			wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: "apache__access_log"})
			wf.Start()
			wf.Input() <- event
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}

			assert.Equal(t, 1, len(result))
			//extracted timestamp
			accessTime, _ := time.Parse("02/Jan/2006:15:04:05 -0700", tc.time)
			assert.Equal(t, accessTime.UTC(), result[0].Time.UTC())

			// check fields
			for k, v := range tc.fields {
				assert.Equal(t, v, result[0].Fields[k], "check '%s' field value", k)
			}
		})
	}
}

func MustParse(layout, value string) time.Time {
	t, err := time.Parse(layout, value)
	if err != nil {
		panic(err)
	}
	return t
}

func TestSyslog(t *testing.T) {
	tz, _ := time.LoadLocation("Asia/Shanghai")

	cases := []struct {
		name   string
		data   string
		events []Event
	}{
		{
			name: "suse syslog",
			data: `
2018-08-14T14:30:02.203151+02:00 linux-sqrz systemd[4179]: Stopped target Basic System.
2018-08-14T14:30:02.203251+02:00 linux-sqrz systemd[4179]: Stopped target Paths.
`,
			events: []Event{
				{
					Time: MustParse("2006-01-02T15:04:05.999999-07:00", "2018-08-14T14:30:02.203151+02:00"),
					Fields: map[string]interface{}{
						"host":    "linux-sqrz",
						"ident":   "systemd",
						"pid":     "4179",
						"message": "Stopped target Basic System.",
						"time":    "2018-08-14T14:30:02.203151+02:00",
					},
				},
				{
					Time: MustParse("2006-01-02T15:04:05.999999-07:00", "2018-08-14T14:30:02.203251+02:00"),
					Fields: map[string]interface{}{
						"host":    "linux-sqrz",
						"ident":   "systemd",
						"pid":     "4179",
						"message": "Stopped target Paths.",
						"time":    "2018-08-14T14:30:02.203251+02:00",
					},
				},
			},
		},
		{
			name: "GoogleSoftwareUpdateAgent",
			data: `
Dec 13 11:35:28 a-mac-with-esc-key GoogleSoftwareUpdateAgent[21412]: 2016-12-13 11:35:28.420 GoogleSoftwareUpdateAgent[21412/0x700007399000] [lvl=2] -[KSAgentApp updateProductWithProductID:usingEngine:] Checking for updates for "All Products" using engine <KSUpdateEngine:0x100341a00
		ticketStore=<KSPersistentTicketStore:0x100204520 store=<KSKeyedPersistentStore:0x100213290
			path="/Users/<USER>/Library/Google/GoogleSoftwareUpdate/TicketStore/Keystone.ticketstore"
			lockFile=<KSLockFile:0x1002160e0
				path="/Users/<USER>/Library/Google/GoogleSoftwareUpdate/TicketStore/Keystone.ticketstore.lock"
				locked=NO
			>
		>>
		processor=<KSActionProcessor:0x1003bb5f0
			delegate=<KSUpdateEngine:0x100341a00>
			isProcessing=NO actionsCompleted=0 progress=0.00
			errors=0 currentActionErrors=0
			events=0 currentActionEvents=0
			actionQueue=( )
		>
		delegate=(null)
		serverInfoStore=(null)
		errors=0
	>
Dec 13 11:35:28 a-mac-with-esc-key GoogleSoftwareUpdateAgent[21412]: 2016-12-13 11:35:28.421 GoogleSoftwareUpdateAgent[21412/0x700007399000] [lvl=2] -[KSUpdateEngine updateAllExceptProduct:] KSUpdateEngine updating all installed products, except:'com.google.Keystone'.
Apr 4 03:39:57 --- last message repeated 1 time ---
			`,
			events: []Event{
				{
					// timestamp has no year, adjust with current year
					// Time: MustParse(time.Stamp, "Dec 13 11:35:28").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 12, 13, 11, 35, 28, 0, tz),
					Fields: map[string]interface{}{
						"host":  "a-mac-with-esc-key",
						"ident": "GoogleSoftwareUpdateAgent",
						"pid":   "21412",
						"message": `2016-12-13 11:35:28.420 GoogleSoftwareUpdateAgent[21412/0x700007399000] [lvl=2] -[KSAgentApp updateProductWithProductID:usingEngine:] Checking for updates for "All Products" using engine <KSUpdateEngine:0x100341a00
		ticketStore=<KSPersistentTicketStore:0x100204520 store=<KSKeyedPersistentStore:0x100213290
			path="/Users/<USER>/Library/Google/GoogleSoftwareUpdate/TicketStore/Keystone.ticketstore"
			lockFile=<KSLockFile:0x1002160e0
				path="/Users/<USER>/Library/Google/GoogleSoftwareUpdate/TicketStore/Keystone.ticketstore.lock"
				locked=NO
			>
		>>
		processor=<KSActionProcessor:0x1003bb5f0
			delegate=<KSUpdateEngine:0x100341a00>
			isProcessing=NO actionsCompleted=0 progress=0.00
			errors=0 currentActionErrors=0
			events=0 currentActionEvents=0
			actionQueue=( )
		>
		delegate=(null)
		serverInfoStore=(null)
		errors=0
	>`,
						"time": "Dec 13 11:35:28",
					},
				},
				{
					// timestamp has no year, adjust with current year
					// Time: MustParse(time.Stamp, "Dec 13 11:35:28").AddDate(time.Now().Year(), 0, 0).,
					Time: time.Date(time.Now().Year(), 12, 13, 11, 35, 28, 0, tz),
					Fields: map[string]interface{}{
						"host":    "a-mac-with-esc-key",
						"ident":   "GoogleSoftwareUpdateAgent",
						"pid":     "21412",
						"message": "2016-12-13 11:35:28.421 GoogleSoftwareUpdateAgent[21412/0x700007399000] [lvl=2] -[KSUpdateEngine updateAllExceptProduct:] KSUpdateEngine updating all installed products, except:'com.google.Keystone'.",
						"time":    "Dec 13 11:35:28",
					},
				},
				{
					// timestamp has no year, adjust with current year
					// Time: MustParse(time.Stamp, "Apr 4 03:39:57").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 4, 4, 3, 39, 57, 0, tz),
					Fields: map[string]interface{}{
						"message": "--- last message repeated 1 time ---",
						"time":    "Apr 4 03:39:57",
					},
				},
			},
		},
		{
			name: "invalid syslog",
			data: `The wonder is, not that the field of stars is so vast, but that man has measured it.`,
			events: []Event{
				{
					Time:   TimeZero,
					Fields: map[string]interface{}{},
				},
			},
		},
		{
			name: "rfc5424",
			data: `<6>1 2021-03-30T15:09:23.099Z dynamice-tailers.net fugiat 56 ID704 - We need to compress the open-source XSS feed!`,
			events: []Event{
				{
					// Time: MustParse("2006-01-02T15:04:05.999Z", "2021-03-30T15:09:23.099Z"),
					Time: time.Date(2021, 3, 30, 15, 9, 23, 99000000, time.UTC),
					Fields: map[string]interface{}{
						"pri":       "6",
						"host":      "dynamice-tailers.net",
						"ident":     "fugiat",
						"pid":       "56",
						"msgid":     "ID704",
						"extradata": "-",
						"message":   "We need to compress the open-source XSS feed!",
						"time":      "2021-03-30T15:09:23.099Z",
					},
				},
			},
		},
		{
			name: "secure",
			data: `
May 14 10:07:54 localhost su: pam_unix(su-l:session): session opened for user root by shtic(uid=500)
May 14 10:43:32 localhost su: pam_unix(su-l:session): session closed for user root
May 14 13:23:17 localhost su: pam_unix(su-l:auth): authentication failure; logname=shtic uid=500 euid=0 tty=pts/0 ruser=shtic rhost=  user=root
May 14 13:23:23 localhost su: pam_unix(su-l:session): session opened for user root by shtic(uid=500)`,
			events: []Event{
				{
					// Time: MustParse("Jan 2 15:04:05", "May 14 10:07:54").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 5, 14, 10, 7, 54, 0, tz),
					Fields: map[string]interface{}{
						"host":    "localhost",
						"ident":   "su",
						"message": "pam_unix(su-l:session): session opened for user root by shtic(uid=500)",
						"time":    "May 14 10:07:54",
					},
				},
				{
					// Time: MustParse("Jan 2 15:04:05", "May 14 10:43:32").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 5, 14, 10, 43, 32, 0, tz),
					Fields: map[string]interface{}{
						"host":    "localhost",
						"ident":   "su",
						"message": "pam_unix(su-l:session): session closed for user root",
						"time":    "May 14 10:43:32",
					},
				},
				{
					// Time: MustParse("Jan 2 15:04:05", "May 14 13:23:17").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 5, 14, 13, 23, 17, 0, tz),
					Fields: map[string]interface{}{
						"host":    "localhost",
						"ident":   "su",
						"message": "pam_unix(su-l:auth): authentication failure; logname=shtic uid=500 euid=0 tty=pts/0 ruser=shtic rhost=  user=root",
						"time":    "May 14 13:23:17",
					},
				},
				{
					// Time: MustParse("Jan 2 15:04:05", "May 14 13:23:23").AddDate(time.Now().Year(), 0, 0),
					Time: time.Date(time.Now().Year(), 5, 14, 13, 23, 23, 0, tz),
					Fields: map[string]interface{}{
						"host":    "localhost",
						"ident":   "su",
						"message": "pam_unix(su-l:session): session opened for user root by shtic(uid=500)",
						"time":    "May 14 13:23:23",
					},
				},
			},
		},
		{
			name: "custom information inserted between timestamp and host",
			data: `<118>Aug 18 02:46:40 2011 datagen-host37 postfix/smtpd[4095]: 5J6EST77VA: client=unknown[**************]`,
			events: []Event{
				{
					Time: time.Date(time.Now().Year(), 8, 18, 2, 46, 40, 0, tz),
					Fields: map[string]interface{}{
						"host":    "datagen-host37",
						"ident":   "postfix/smtpd",
						"pid":     "4095",
						"pri":     "118",
						"message": "5J6EST77VA: client=unknown[**************]",
						"time":    "Aug 18 02:46:40",
					},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			datatype := stonewave.Datatype{
				Name: "syslog",
			}

			emitter, err := NewEmitterForDatatype(&datatype, strings.NewReader(tc.data))
			assert.NoError(t, err)

			wf := NewWorkflowForDatatype(&datatype)
			wf.Start()

			for {
				event, err := emitter.Emit()
				if err != nil {
					break
				}
				wf.Input() <- event
			}
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tc.events), len(result))
			for i, expected := range tc.events {
				assert.Equal(t, expected.Time.UnixNano(), result[i].Time.UnixNano())
				assert.Equal(t, expected.Fields, result[i].Fields)
			}
		})
	}
}

func TestNewWorkflowForDatatype(t *testing.T) {
	testCases := []struct {
		name      string
		datatype  stonewave.Datatype
		nodeNames []string
	}{
		{
			name: "nginx__access_log",
			datatype: stonewave.Datatype{
				Name: "nginx__access_log",
			},
			nodeNames: []string{"regexp", "date", "generate_raw", "flatten"},
		},
		{
			name: "csv",
			datatype: stonewave.Datatype{
				IngestionTimeExtraction: "csv",
				IngestionTimeFieldNames: []string{"a", "b", "c"},
			},
			nodeNames: []string{"csv", "generate_raw", "flatten"},
		},
		{
			name: "csv without field names",
			datatype: stonewave.Datatype{
				IngestionTimeExtraction: "csv",
			},
			nodeNames: []string{"generate_raw", "flatten"},
		},
		{
			name: "custom datatype without yaml workflow",
			datatype: stonewave.Datatype{
				Name:                    "test1",
				IngestionTimeExtraction: "json",
			},
			nodeNames: []string{"json", "generate_raw", "flatten"},
		},
		{
			name: "discard raw message",
			datatype: stonewave.Datatype{
				IngestionTimeExtraction: "json",
				DiscardRawMessage:       true,
			},
			nodeNames: []string{"json", "remove", "flatten"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			wf := NewWorkflowForDatatype(&tc.datatype)
			assert.Equal(t, len(tc.nodeNames), len(wf.processorCfgs))
			for i := range tc.nodeNames {
				c := wf.processorCfgs[i]
				for k := range c {
					assert.Equal(t, tc.nodeNames[i], k)
				}
			}
		})
	}
}

func TestYHPLog(t *testing.T) {
	cases := []struct {
		name   string
		data   string
		time   time.Time
		fields map[string]string
	}{
		{
			name: "stonewave-slice-compactor",
			data: `[2021-08-04T08:08:30.292578+00:00] [debug] [6] [32] [slice_compaction_service] msg='event set is not enabled for compaction' event_set='nina_yh_test_indexer'`,
			time: TimeZero,
			fields: map[string]string{
				"log_level":   "debug",
				"process_id":  "6",
				"thread_id":   "32",
				"logger_name": "slice_compaction_service",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			event := NewEvent()
			event.Message = tc.data

			wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: "yhp__log"})
			wf.Start()
			wf.Input() <- event
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}

			assert.Equal(t, 1, len(result))
			assert.Equal(t, tc.time, result[0].Time)

			// check fields
			for k, v := range tc.fields {
				assert.Equal(t, v, result[0].Fields[k], "check '%s' field value", k)
			}
		})
	}
}

func TestEventFiltered(t *testing.T) {
	cases := []struct {
		name     string
		data     string
		datatype string
	}{
		{
			name:     "empty message",
			data:     ``,
			datatype: "plain_text",
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			event := NewEvent()
			event.Message = tc.data

			wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: tc.datatype})
			wf.Start()
			wf.Input() <- event
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}

			assert.Equal(t, 1, len(result))
			assert.Nil(t, result[0])
		})
	}
}

func TestJson(t *testing.T) {
	cases := []struct {
		name   string
		input  Event
		fields map[string]interface{}
		want   Event
	}{
		{
			name: "simple json",
			input: Event{
				Message: `{"a":1}`,
			},
			want: Event{
				Message: `{"a":1}`,
				Fields:  map[string]interface{}{"a": json.Number("1")},
			},
		},
		{
			name: "generate _message",
			input: Event{
				Fields: map[string]interface{}{"a": 1},
			},
			want: Event{
				Message: `{"a":1}`,
				Fields:  map[string]interface{}{"a": 1},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			event := tc.input

			wf := NewWorkflowForDatatype(&stonewave.Datatype{Name: "json", IngestionTimeExtraction: "json"})
			wf.Start()
			wf.Input() <- &event
			wf.AsyncClose()

			var result []*Event
			for e := range wf.Output() {
				result = append(result, e)
			}

			assert.Equal(t, 1, len(result))
			assert.Equal(t, tc.want.Message, result[0].Message)
			assert.Equal(t, tc.want.Fields, result[0].Fields)
		})
	}
}
