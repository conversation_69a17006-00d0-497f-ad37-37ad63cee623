/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"sort"
)

type Flatten struct{}

func FlattenBuilder(_ Options) (Processor, error) {
	f := new(Flatten)
	return f, nil
}

var maxFields = 200

func (*Flatten) Process(e *Event) (*Event, error) {
	keys := make([]string, 0, len(e.<PERSON>))
	for k := range e.Fields {
		keys = append(keys, k)
	}

	for _, k := range keys {
		v := e.Fields[k]
		switch v.(type) {
		case map[string]interface{}:
			delete(e.Fields, k)
			flatten(e.Fields, k, v, false)
		case []interface{}:
			delete(e.Fields, k)
			flatten(e.Fields, k, v, true)
		}
	}

	if len(e.Fields) > maxFields {
		keys := make([]string, 0, len(e.Fields))
		for k := range e.Fields {
			keys = append(keys, k)
		}
		sort.Strings(keys)

		m := make(map[string]interface{}, maxFields)
		for _, k := range keys {
			m[k] = e.Fields[k]
		}
		e.Fields = m
	}

	return e, nil
}

func flatten(m map[string]interface{}, prefix string, value interface{}, isSlice bool) {
	switch tv := value.(type) {
	case map[string]interface{}:
		if prefix != "" {
			prefix += "."
		}
		for mk, mv := range tv {
			flatten(m, prefix+mk, mv, isSlice)
		}
	case []interface{}:
		prefix += "[]"
		if len(tv) == 0 {
			m[prefix] = []interface{}{}
		}
		for _, item := range tv {
			flatten(m, prefix, item, true)
		}
	default:
		if isSlice {
			var s []interface{}
			if mv, ok := m[prefix]; ok {
				s, _ = mv.([]interface{})
			}
			m[prefix] = append(s, tv)
		} else {
			m[prefix] = tv
		}
	}
}
