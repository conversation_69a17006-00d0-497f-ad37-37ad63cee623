/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	_ "embed"
	"errors"
	"io/ioutil"
	"nile/pkg/stonewave"
	"path/filepath"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gopkg.in/yaml.v3"
)

type processorBuilder func(Options) (Processor, error)
type builderManager struct {
	Processor map[string]processorBuilder
}

var BuilderManager = builderManager{
	Processor: map[string]processorBuilder{
		"grok":         GrokBuilder,
		"remove":       Remove<PERSON>uilder,
		"user_agent":   UserAgentBuilder,
		"date":         DateBuilder,
		"json":         JSONBuilder,
		"regexp":       RegexpBuilder,
		"flatten":      FlattenBuilder,
		"csv":          CSVBuilder,
		"generate_raw": GenerateRawMessageBuilder,
	},
}

// bufferSize is the capacity of Workflow channels
const bufferSize = 16

var ErrProcessPanic = errors.New("processor panic")
var ErrSinkPanic = errors.New("sink panic")

type Workflow struct {
	datatype      *stonewave.Datatype
	processorCfgs NodesConfig
	processors    []Processor

	input  chan *Event
	output chan *Event

	group *errgroup.Group
}

func NewWorkflowForDatatype(datatype *stonewave.Datatype) *Workflow {
	wf := &Workflow{
		datatype: datatype,
	}

	wf.processorCfgs = DefaultWorkflowConfig[datatype.Name]

	// use catalog to generate workflow
	if wf.processorCfgs == nil {
		switch datatype.IngestionTimeExtraction {
		case "csv":
			if len(datatype.IngestionTimeFieldNames) > 0 {
				wf.processorCfgs = append(wf.processorCfgs, map[string]Options{
					"csv": {
						"delimiter":                  datatype.Delimiter,
						"ingestion_time_field_names": datatype.IngestionTimeFieldNames,
					},
				})
			}
		case "json":
			wf.processorCfgs = append(wf.processorCfgs,
				map[string]Options{
					"json": nil,
				},
			)
		}
	}

	// append discard raw message node
	if datatype.DiscardRawMessage {
		wf.processorCfgs = append(wf.processorCfgs,
			map[string]Options{
				"remove": {"field": "_message"},
			})
	} else {
		wf.processorCfgs = append(wf.processorCfgs,
			map[string]Options{
				"generate_raw": {"keep_fields": datatype.IngestionTimeExtraction == "json"},
			})
	}

	wf.processorCfgs = append(wf.processorCfgs,
		map[string]Options{
			"flatten": nil,
		})

	return wf
}

// NewFileWorkflowForDatatype generate workflow for file upload
func NewFileWorkflowForDatatype(datatype *stonewave.Datatype) *Workflow {
	wf := NewWorkflowForDatatype(datatype)

	// do not change workflow for custom datatype
	if _, has := DefaultWorkflowConfig[datatype.Name]; has {
		return wf
	}

	if datatype.IngestionTimeExtraction == "json" && len(wf.processorCfgs) >= 1 {
		// skip json parsing as emitter_json already do it
		wf.processorCfgs = wf.processorCfgs[1:]
	}

	return wf
}

func NewWorkflowForDatatypeWithPreprocessor(datatype *stonewave.Datatype, p map[string]Options) *Workflow {
	wf := &Workflow{
		datatype: datatype,
	}

	processorCfgs := DefaultWorkflowConfig[datatype.Name]

	// use catalog to generate workflow
	if wf.processorCfgs == nil {
		switch datatype.IngestionTimeExtraction {
		case "csv":
			if len(datatype.IngestionTimeFieldNames) > 0 {
				processorCfgs = append(wf.processorCfgs, map[string]Options{
					"csv": {
						"delimiter":                  datatype.Delimiter,
						"ingestion_time_field_names": datatype.IngestionTimeFieldNames,
					},
				})
			}
		case "json":
			processorCfgs = append(wf.processorCfgs,
				map[string]Options{
					"json": nil,
				},
			)
		}
	}

	wf.processorCfgs = make(NodesConfig, 0, len(processorCfgs)+3)
	wf.processorCfgs = append(wf.processorCfgs, p)
	wf.processorCfgs = append(wf.processorCfgs, processorCfgs...)

	// append discard raw message node
	if datatype.DiscardRawMessage {
		wf.processorCfgs = append(wf.processorCfgs,
			map[string]Options{
				"remove": {"field": "_message"},
			})
	} else {
		wf.processorCfgs = append(wf.processorCfgs,
			map[string]Options{
				"generate_raw": nil,
			})
	}

	wf.processorCfgs = append(wf.processorCfgs,
		map[string]Options{
			"flatten": nil,
		})

	return wf
}

func (wf *Workflow) Start() {
	wf.input = make(chan *Event, bufferSize)
	wf.output = wf.input

	wf.group = new(errgroup.Group)
	for _, c := range wf.processorCfgs {
		name, options := GetNodeConfig(c)
		pb, ok := BuilderManager.Processor[name]
		if !ok {
			zap.S().Infow("no processor builder", "type", name)
			continue
		}

		proc, err := pb(options)
		if err != nil {
			zap.S().Infow("fail in create processor", "type", name, "options", options)
			continue
		}

		in := wf.output
		out := make(chan *Event, bufferSize)

		wf.group.Go(func() (err error) {
			defer func() {
				if r := recover(); r != nil {
					zap.S().Errorw("panic in running processor", "error", r)
					err = ErrProcessPanic
				}
			}()
			runProcessor(proc, in, out)
			return
		})

		wf.output = out
	}
}

func (wf *Workflow) StartWithOutput(output chan *Event) {
	wf.input = make(chan *Event, bufferSize)
	wf.output = wf.input

	wf.group = new(errgroup.Group)
	for i, c := range wf.processorCfgs {
		name, options := GetNodeConfig(c)
		pb, ok := BuilderManager.Processor[name]
		if !ok {
			zap.S().Infow("no processor builder", "type", name)
			continue
		}

		proc, err := pb(options)
		if err != nil {
			zap.S().Infow("fail in create processor", "type", name, "options", options)
			continue
		}

		in := wf.output
		var out chan *Event
		if i < len(wf.processorCfgs)-1 {
			out = make(chan *Event, bufferSize)
		} else {
			out = output
		}

		wf.group.Go(func(i int) func() error {
			return func() (err error) {
				defer func() {
					if r := recover(); r != nil {
						zap.S().Errorw("panic in running processor", "error", r)
						err = ErrProcessPanic
					}
				}()
				if i < len(wf.processorCfgs)-1 {
					runProcessor(proc, in, out)
				} else {
					runProcessorNoCloser(proc, in, out)
				}
				return
			}
		}(i))

		wf.output = out
	}
}

func (wf *Workflow) AsyncClose() {
	close(wf.input)
}

func (wf *Workflow) Input() chan<- *Event {
	return wf.input
}

func (wf *Workflow) Output() <-chan *Event {
	return wf.output
}

func (wf *Workflow) WaitClose() error {
	return wf.group.Wait()
}

func (wf *Workflow) Process(e *Event) *Event {
	if wf.processors == nil {
		wf.processors = make([]Processor, 0, len(wf.processorCfgs))

		for _, c := range wf.processorCfgs {
			name, options := GetNodeConfig(c)
			pb, ok := BuilderManager.Processor[name]
			if !ok {
				zap.S().Infow("no processor builder", "type", name)
				continue
			}

			proc, err := pb(options)
			if err != nil {
				zap.S().Infow("fail in create processor", "type", name, "options", options)
				continue
			}

			wf.processors = append(wf.processors, proc)
		}
	}

	for _, proc := range wf.processors {
		e, _ := proc.Process(e)
		if e == nil {
			break
		}
	}
	return e
}

func (wf *Workflow) GetNodeConfig() NodesConfig {
	return wf.processorCfgs
}

type WorkflowConfig map[string]NodesConfig

//go:embed workflow.yaml
var defaultWorkflow []byte
var DefaultWorkflowConfig WorkflowConfig = MustLoadNodesConfigFromYAML(defaultWorkflow)

type NodesConfig []map[string]Options

func GetNodeConfig(n map[string]Options) (string, Options) {
	for k, v := range n {
		return k, v
	}
	return "", nil
}

func LoadNodesConfigFromYAML(data []byte) (map[string]NodesConfig, error) {
	workflows := make(map[string][]map[string]map[string]interface{})
	err := yaml.Unmarshal(data, &workflows)
	if err != nil {
		return nil, err
	}

	configs := make(map[string]NodesConfig, len(workflows))
	for wfName, wf := range workflows {
		nc := make([]map[string]Options, len(wf))
		for j, node := range wf {
			var nodeOptions map[string]Options
			for nodeName, nodeOpt := range node {
				nodeOptions = make(map[string]Options, len(nodeOpt))
				nodeOptions[nodeName] = nodeOpt
			}
			nc[j] = nodeOptions
		}

		configs[wfName] = nc
	}
	return configs, nil
}

func MustLoadNodesConfigFromYAML(data []byte) map[string]NodesConfig {
	c, err := LoadNodesConfigFromYAML(data)
	if err != nil {
		panic(err)
	}
	return c
}

func (wc WorkflowConfig) LoadNodesConfigFromYAML(data []byte) error {
	c, err := LoadNodesConfigFromYAML(data)
	if err != nil {
		return err
	}

	for k, v := range c {
		wc[k] = v
	}

	return nil
}

func (wc WorkflowConfig) LoadNodesConfigInDir(dirname string) {
	fs, err := ioutil.ReadDir(dirname)
	if err != nil {
		return
	}

	for _, f := range fs {
		file := f.Name()

		if filepath.Ext(file) != ".yaml" {
			continue
		}

		zap.S().Infow("load  workflow", "file", file)

		data, err := ioutil.ReadFile(filepath.Join(dirname, file))
		if err != nil {
			zap.S().Infow("failed in read file", "file", file)
			continue
		}

		if err = wc.LoadNodesConfigFromYAML(data); err != nil {
			zap.S().Infow("failed in load workflow configure file", "file", file, "error", err)
		}
	}
}

// func IsWorkflowEqual(wf1, wf2 *Workflow) bool {
// 	if len(wf1.processorCfgs) != len(wf2.processorCfgs) {
// 		return false
// 	}

// 	for i := 0; i < len(wf1.processorCfgs); i++ {
// 		proc1 := wf1.processorCfgs[i]
// 		proc2 := wf2.processorCfgs[i]
// 		if len(proc1) != len(proc2) {
// 			return false
// 		}

// 		for k, opt1 := range proc1 {
// 			opt2 := proc2[k]
// 			if !IsOptionsEqual(opt1, opt2) {
// 				return false
// 			}
// 		}
// 	}

// 	return true
// }
