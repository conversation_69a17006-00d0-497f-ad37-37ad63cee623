/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"encoding/csv"
	"errors"
	"fmt"
	"strings"
	"unicode/utf8"
)

type CSV struct {
	Delimiter string   `option:"delimiter,required"`
	Fields    []string `option:"ingestion_time_field_names,required"`

	delimiter rune
}

func CSVBuilder(o Options) (Processor, error) {
	c := new(CSV)
	if err := o.Apply(c); err != nil {
		return nil, err
	}

	c.delimiter, _ = utf8.DecodeRuneInString(c.Delimiter)
	if c.delimiter == utf8.RuneError {
		return nil, errors.New("invalid utf-8 delimiter")
	}

	for i := 0; i < len(c.Fields); i++ {
		c.Fields[i] = strings.ReplaceAll(strings.TrimSpace(c.<PERSON>[i]), "\n", "_")
	}
	return c, nil
}

func (c *CSV) Process(e *Event) (*Event, error) {
	// init csv reader
	r := csv.NewReader(strings.NewReader(e.Message))
	r.Comma = c.delimiter
	r.LazyQuotes = true
	if c.delimiter != ' ' && c.delimiter != '\t' {
		r.TrimLeadingSpace = true
	}

	record, err := r.Read()
	if err != nil && err != csv.ErrFieldCount {
		return e, err
	}

	// remove event if all fields are blank
	blankLine := true
	for i := 0; i < len(record); i++ {
		record[i] = strings.TrimSpace(strings.ReplaceAll(record[i], "\n", " "))
		if len(record[i]) != 0 {
			blankLine = false
		}
	}
	if blankLine {
		return nil, nil
	}

	for i, r := range record {
		if r != "" {
			if i < len(c.Fields) {
				e.SetRealField(c.Fields[i], r)
			} else {
				e.SetRealField(fmt.Sprintf("EXTRA_FIELD_%d", i+1), r)
			}

		}
	}

	return e, nil
}
