/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"github.com/vjeantet/grok"
)

type Grok struct {
	// The field to use for grok expression parsing
	Field string `option:"field,,_message"`
	// An ordered list of grok expression to match and extract named captures with.
	// Returns on the first expression in the list that matches.
	Patterns []string `option:"patterns,required"`
	// A map of pattern-name and pattern tuples defining custom patterns to be used by the current processor.
	// Patterns matching existing names will override the pre-existing definition.
	PatternDefinitions map[string]string `option:"pattern_definitions"`

	grok *grok.Grok
}

func GrokBuilder(o Options) (Processor, error) {
	g := new(Grok)

	err := o.Apply(g)
	if err != nil {
		return nil, err
	}

	g.grok, err = grok.NewWithConfig(&grok.Config{
		NamedCapturesOnly: true,
		RemoveEmptyValues: true,
		Patterns:          g.PatternDefinitions,
	})
	if err != nil {
		return nil, err
	}

	return g, nil
}

func (gp *Grok) Process(e *Event) (*Event, error) {
	source := e.GetStringField(gp.Field)

	for _, p := range gp.Patterns {
		fields, err := gp.grok.ParseTyped(p, source)
		if err == nil && len(fields) > 0 {
			for k, v := range fields {
				e.SetRealField(k, v)
			}
			break
		}
	}
	return e, nil
}
