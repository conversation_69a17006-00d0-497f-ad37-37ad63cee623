/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"bufio"
	"bytes"
	"io"
	"time"

	jsoniter "github.com/json-iterator/go"
)

type HEIEmitter struct {
	// Datatype string `option:"datatype,,json"`

	r *bufio.Reader
}

func NewHEIEmitter(r io.Reader, o Options) (Emitter, error) {
	e := new(HEIEmitter)
	err := o.Apply(e)
	if err != nil {
		return nil, err
	}

	var ok bool
	if e.r, ok = r.(*bufio.Reader); !ok {
		e.r = bufio.NewReader(r)
	}

	return e, nil
}

func (hei *HEIEmitter) Emit() (*Event, error) {
	var v map[string]interface{}
	for {
		line, lineErr := hei.r.ReadBytes('\n')

		if len(line) > 0 {
			if err := jsoniter.Unmarshal(line, &v); err == nil {
				event := NewEvent()
				// event.Datatype = hei.Datatype
				if t, has := v["_time"]; has {
					switch tt := t.(type) {
					case int64:
						event.Time = time.Unix(tt/1000000, tt*1000%1000000)
						delete(v, "_time")
					case float64:
						event.Time = time.Unix(int64(tt)/1000000, int64(tt)*1000%1000000)
						delete(v, "_time")
					}
				}

				if msg, has := v["_message"]; has {
					event.Message, _ = msg.(string)
					delete(v, "_message")

					// when _message is set, extract the other fields from hei message
					for field, fv := range v {
						event.SetField(field, fv)
					}
				} else {
					// data, err := jsoniter.Marshal(v)
					// if err == nil {
					// 	event.Message = string(data)
					// } else {
					event.Message = string(bytes.TrimSpace(line))
					// }
				}
				return event, nil
			}
		}

		if lineErr != nil {
			return nil, io.EOF
		}
	}
}
