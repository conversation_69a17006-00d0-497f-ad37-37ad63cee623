/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUserAgent(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		input  Event
		output Event
	}{
		{
			name: "no source field",
			option: Options{
				"field": "no_such_field",
			},
			input: Event{
				Time:    TimeZero,
				Message: "Test Message",
				Fields: map[string]interface{}{
					"field1": "value1",
				},
			},
			output: Event{
				Time:    TimeZero,
				Message: "Test Message",
				Fields: map[string]interface{}{
					"field1": "value1",
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			g, err := UserAgentBuilder(tc.option)
			assert.NoError(t, err)

			e, err := g.Process(&tc.input)
			assert.NoError(t, err)
			assert.Equal(t, tc.output, *e)
		})
	}
}
