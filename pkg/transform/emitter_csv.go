/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"bufio"
	"bytes"
	"errors"
	"io"
	"unicode"
	"unicode/utf8"
)

type CSVEmitter struct {
	Delimiter string `option:"delimiter,required"`
	// MaxLines  int    `option:"max_lines,,16"`
	// Fields    []string `option:"ingestion_time_field_names"`

	delimiter        rune
	delimiterLen     int
	trimLeadingSpace bool
	r                *bufio.Reader
	buf              []byte
}

func NewCSVEmitter(r io.Reader, o Options) (Emitter, error) {
	ce := new(CSVEmitter)
	err := o.Apply(ce)
	if err != nil {
		return nil, err
	}

	ce.delimiter, _ = utf8.DecodeRuneInString(ce.Delimiter)
	if ce.delimiter == utf8.RuneError {
		return nil, errors.New("invalid utf-8 delimiter")
	}
	ce.delimiterLen = utf8.RuneLen(ce.delimiter)

	var ok bool
	if ce.r, ok = r.(*bufio.Reader); !ok {
		ce.r = bufio.NewReader(r)
	}

	if ce.delimiter != ' ' && ce.delimiter != '\t' {
		ce.trimLeadingSpace = true
	}

	ce.buf = make([]byte, 0, 1024)

	return ce, nil
}

// lengthNL reports the number of bytes for the trailing \n.
func lengthNL(b []byte) int {
	if len(b) > 0 && b[len(b)-1] == '\n' {
		return 1
	}
	return 0
}

// nextRune returns the next rune in b or utf8.RuneError.
func nextRune(b []byte) rune {
	r, _ := utf8.DecodeRune(b)
	return r
}

func (ce *CSVEmitter) readLine() {
	line, err := ce.r.ReadSlice('\n')
	ce.buf = append(ce.buf, line...)
	for err == bufio.ErrBufferFull {
		line, err = ce.r.ReadSlice('\n')
		ce.buf = append(ce.buf, line...)
	}
}

func (ce *CSVEmitter) getNext() {
	ce.buf = ce.buf[:0]
	ce.readLine()

	const quoteLen = len(`"`)
	var pos int
parseField:
	for {
		// find first non-whitespace
		i := bytes.IndexFunc(ce.buf[pos:], func(r rune) bool {
			return !unicode.IsSpace(r)
		})
		if i < 0 {
			i = len(ce.buf[pos:])
		}
		pos += i

		if len(ce.buf[pos:]) == 0 || ce.buf[pos] != '"' {
			// Non-quoted string field
			i := bytes.IndexRune(ce.buf[pos:], ce.delimiter)
			if i >= 0 {
				pos += i + ce.delimiterLen
				continue parseField
			}
			break parseField
		} else {
			// Quoted string field
			pos += quoteLen
			for {
				i := bytes.IndexByte(ce.buf[pos:], '"')
				if i >= 0 {
					// Hit next quote.
					pos += i + quoteLen
					switch rn := nextRune(ce.buf[pos:]); {
					case rn == '"':
						// `""` sequence (append quote).
						pos += quoteLen
					case rn == ce.delimiter:
						// `",` sequence (end of field).
						pos += ce.delimiterLen
						continue parseField
					case lengthNL(ce.buf[pos:]) == len(ce.buf[pos:]):
						// `"\n` sequence (end of line).
						break parseField
					default:
						// `"*` sequence (invalid non-escaped quote).
						break parseField
					}
				} else if len(ce.buf[pos:]) > 0 {
					// Hit end of line (copy all data so far).
					pos = len(ce.buf)
					ce.readLine()
				} else {
					// Abrupt end of file (EOF or error).
					break parseField
				}
			}
		}
	}
}

func (ce *CSVEmitter) Emit() (*Event, error) {
	for {
		ce.getNext()
		if len(ce.buf) == 0 {
			return nil, io.EOF
		}

		m := string(bytes.TrimSpace(ce.buf))
		if len(m) > 0 {
			event := NewEvent()
			event.Message = m
			return event, nil
		}
	}
}
