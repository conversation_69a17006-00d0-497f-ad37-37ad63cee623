/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"io"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestHEIEmitter(t *testing.T) {
	cases := []struct {
		name   string
		data   string
		events []Event
	}{
		{
			name: "with _time",
			data: `{"_message": "message1", "_time": 1}
			{"_message": "message2", "_time": 2}
			{"_message": "message3", "_time": 3}`,
			events: []Event{
				{
					Time:    time.Unix(0, 1000),
					Message: `message1`,
				},
				{
					Time:    time.Unix(0, 2000),
					Message: `message2`,
				},
				{
					Time:    time.Unix(0, 3000),
					Message: `message3`,
				},
			},
		},
		{
			name: "no message field",
			data: `{"a": 1, "b": "x", "c": true}`,
			events: []Event{
				{
					Message: `{"a": 1, "b": "x", "c": true}`,
					// Fields: map[string]interface{}{
					// 	"a": float64(1),
					// 	"b": "x",
					// 	"c": true,
					// },
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			emitter, err := NewHEIEmitter(strings.NewReader(tc.data), nil)
			assert.NoError(t, err)

			var emittedEvents []Event
			for {
				event, err := emitter.Emit()
				if err == io.EOF {
					break
				}
				emittedEvents = append(emittedEvents, *event)
			}

			assert.Equal(t, len(tc.events), len(emittedEvents))

			for i, e := range tc.events {
				assert.Equal(t, e.Message, emittedEvents[i].Message)
				if e.Fields == nil {
					e.Fields = make(map[string]interface{})
				}
				assert.Equal(t, e.Fields, emittedEvents[i].Fields)
			}
		})
	}
}
