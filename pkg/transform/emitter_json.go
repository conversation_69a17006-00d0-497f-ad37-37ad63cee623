/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"bufio"
	"bytes"
	"io"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

var jsonAPI = jsoniter.Config{
	EscapeHTML: true,
	UseNumber:  true,
}.Froze()

var jsonAPIOrderedKeys = jsoniter.Config{
	EscapeHTML:  true,
	UseNumber:   true,
	SortMapKeys: true,
}.Froze()

type JSONEmitter struct {
	MaxEventSize int `option:"max_event_size,,1048576"`

	// MaxFields is the max number of fields extracted in processing
	MaxFields int `option:"max_fields,,200"`

	r            *bufio.Reader
	buf          []byte
	cachedEvents []*Event
}

func NewJSONEmitter(r io.Reader, o Options) (Emitter, error) {
	e := new(JSONEmitter)
	err := o.Apply(e)
	if err != nil {
		return nil, err
	}

	var ok bool
	if e.r, ok = r.(*bufio.Reader); !ok {
		e.r = bufio.NewReader(r)
	}

	return e, nil
}

func (je *JSONEmitter) Emit() (*Event, error) {
	if len(je.cachedEvents) > 0 {
		var e *Event
		e, je.cachedEvents = je.cachedEvents[0], je.cachedEvents[1:]
		return e, nil
	}

	if len(je.buf) > 0 {
		return je.emitLine()
	}

	for {
		line, lineErr := je.r.ReadBytes('\n')

		if len(line) > 0 {
			je.buf = append(je.buf, line...)

			var parsed interface{}
			if err := jsonAPI.Unmarshal(je.buf, &parsed); err == nil {
				switch pt := parsed.(type) {
				case map[string]interface{}:
					event := NewEvent()
					event.Message = string(bytes.TrimSpace(je.buf))

					n := 0
					for k, v := range pt {
						event.SetRealField(k, v)
						if n += 1; n == je.MaxFields {
							break
						}
					}

					je.buf = je.buf[:0]
					return event, nil
				case []interface{}:
					je.cachedEvents = make([]*Event, 0, len(pt))
					je.generateEventsFromInterface(pt)

					je.buf = je.buf[:0]
					return je.emitEventFromCache()
				}
			}
		}

		if lineErr == io.EOF {
			// flush data in buffer if any
			return je.emitLine()
		}

		if len(je.buf) >= je.MaxEventSize || lineErr != nil {
			zap.S().Debugw("Fail in parse json. Emit event for each line.", "bufferSize", len(je.buf), "readLineError", lineErr, "buffer", string(je.buf[:256]))
			return je.emitLine()
		}
	}
}

func (je *JSONEmitter) emitLine() (*Event, error) {
	for {
		if len(je.buf) == 0 {
			return nil, io.EOF
		}
		var msg []byte
		i := bytes.IndexByte(je.buf, '\n')
		if i == -1 {
			msg = je.buf
			je.buf = je.buf[:0]
		} else {
			msg = je.buf[:i]
			je.buf = je.buf[i+1:]
		}
		msg = bytes.TrimSpace(msg)
		if len(msg) == 0 {
			continue
		}

		event := NewEvent()
		event.Message = string(msg)
		// try to parse the line as json
		var parsed interface{}
		if err := jsonAPI.Unmarshal(msg, &parsed); err == nil {
			switch pt := parsed.(type) {
			case map[string]interface{}:
				n := 0
				for k, v := range pt {
					event.SetRealField(k, v)
					if n += 1; n == je.MaxFields {
						break
					}
				}
			}
		}
		return event, nil

	}
}

func (je *JSONEmitter) generateEventsFromInterface(obj interface{}) {
	switch pt := obj.(type) {
	case map[string]interface{}:
		event := NewEvent()
		event.Message, _ = jsonAPIOrderedKeys.MarshalToString(pt)

		n := 0
		for k, v := range pt {
			event.SetRealField(k, v)
			if n += 1; n == je.MaxFields {
				break
			}
		}

		je.cachedEvents = append(je.cachedEvents, event)

	case []interface{}:
		for _, item := range pt {
			je.generateEventsFromInterface(item)
		}
	}
}

func (je *JSONEmitter) emitEventFromCache() (*Event, error) {
	var e *Event
	e, je.cachedEvents = je.cachedEvents[0], je.cachedEvents[1:]
	return e, nil
}
