/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEventBreaker(t *testing.T) {
	cases := []struct {
		name   string
		chunk  string
		events []string
	}{
		{
			name:   "line break",
			chunk:  "line1\nline2\nline3",
			events: []string{"line1", "line2", "line3"},
		},
		{
			name:   "single line",
			chunk:  "single line",
			events: []string{"single line"},
		},
		{
			name:   "no content",
			chunk:  "",
			events: nil,
		},
		{
			name:   "trim whitespace",
			chunk:  " line1\n line2 \nline 3\n",
			events: []string{"line1", "line2", "line 3"},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			eb, err := NewEventBreaker(strings.NewReader(tc.chunk), Options{})
			assert.NoError(t, err)

			var events []string
			for {
				ne, err := eb.Emit()
				if err == io.EOF {
					break
				}
				assert.NoError(t, err)
				events = append(events, ne.Message)
			}

			assert.Equal(t, tc.events, events)
		})
	}
}

func TestFirstlineFormat(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		chunk  string
		events []string
	}{
		{
			name:   "basic",
			option: Options{"firstline_format": "NEWLINE"},
			chunk:  "[NEWLINE]line1\nline2[NEWLINE]\n[NEWLINE] line3\n",
			events: []string{"[NEWLINE]line1", "line2[NEWLINE]", "[NEWLINE] line3"},
		},
		{
			name:   "content before firstline format",
			option: Options{"firstline_format": "NEWLINE"},
			chunk:  "line1\nline2[NEWLINE]\n[NEWLINE]line3\n",
			events: []string{"line1", "line2[NEWLINE]", "[NEWLINE]line3"},
		},
		{
			name:   "no firstline",
			option: Options{"firstline_format": "NEWLINE"},
			chunk:  "line1\nline2\nline3\n",
			events: []string{"line1\nline2\nline3"},
		},
		{
			name:   "multiline",
			option: Options{"firstline_format": "NEWLINE"},
			chunk:  "[NEWLINE]line1\nline2\nline2.5\n[NEWLINE]line3\n",
			events: []string{"[NEWLINE]line1\nline2\nline2.5", "[NEWLINE]line3"},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			eb, err := NewEventBreaker(strings.NewReader(tc.chunk), tc.option)
			assert.NoError(t, err)

			var events []string
			for {
				ne, err := eb.Emit()
				if err == io.EOF {
					break
				}
				assert.NoError(t, err)
				events = append(events, ne.Message)
			}

			assert.Equal(t, tc.events, events)
		})
	}
}

func TestEventBreakerMaxLinesConfig(t *testing.T) {
	cases := []struct {
		name     string
		options  Options
		maxLines int
	}{
		{
			name:     "default",
			options:  Options{},
			maxLines: 256,
		},
		{
			name:     "set max_lines",
			options:  Options{"max_lines": 666},
			maxLines: 666,
		},
		{
			name:     "string type",
			options:  Options{"max_lines": "1"},
			maxLines: 256,
		},
		{
			name:     "negative value",
			options:  Options{"max_lines": -1},
			maxLines: 1,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			eb, err := NewEventBreaker(nil, tc.options)
			assert.NoError(t, err)
			assert.Equal(t, tc.maxLines, eb.(*EventBreaker).MaxLines)
		})
	}
}

func TestEventBreakerMaxLines(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		chunk  string
		events []string
	}{
		// {
		// 	name:   "one line",
		// 	option: Options{"firstline_format": "NEWLINE", "max_lines": 1},
		// 	chunk: `[NEWLINE]line1
		// 	line2
		// 	[NEWLINE] line3`,
		// 	events: []string{"[NEWLINE]line1", "line2", "[NEWLINE] line3"},
		// },
		// {
		// 	name:   "two lines",
		// 	option: Options{"firstline_format": "NEWLINE", "max_lines": 2},
		// 	chunk:  "[NEWLINE]line1\nline2\n[NEWLINE]line3\n[NEWLINE]line4\nline5\nline6\n[NEWLINE]line7\n[NEWLINE]line8\nline9\nline10",
		// 	events: []string{"[NEWLINE]line1\nline2", "[NEWLINE]line3", "[NEWLINE]line4\nline5", "line6", "[NEWLINE]line7", "[NEWLINE]line8\nline9", "line10"},
		// },
		{
			name:   "read all lines",
			option: Options{"firstline_format": "NEWLINE", "max_lines": 10},
			chunk:  "[NEWLINE]line1\nline2\nline3",
			events: []string{"[NEWLINE]line1\nline2\nline3"},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			eb, err := NewEventBreaker(strings.NewReader(tc.chunk), tc.option)
			assert.NoError(t, err)

			var events []string
			for {
				ne, err := eb.Emit()
				if err == io.EOF {
					break
				}
				assert.NoError(t, err)
				events = append(events, ne.Message)
			}

			assert.Equal(t, tc.events, events)
		})
	}
}
