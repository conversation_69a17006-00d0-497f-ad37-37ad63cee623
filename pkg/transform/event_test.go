/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestToPreviewString(t *testing.T) {
	cases := []struct {
		name  string
		event *Event
		want  string
	}{
		{
			name:  "empty",
			event: NewEvent(),
			want:  `{"_datatype":"plain_text", "_message":"", "_time":0}`,
		},
		{
			name: "fields",
			event: &Event{
				Time: TimeZero,
				Fields: map[string]interface{}{
					"_source": "test",
					"_host":   "local",
				},
			},
			want: `{"_datatype":"","_host":"local","_message":"","_source":"test","_time":0}`,
		},
		{
			name: "_time",
			event: &Event{
				Time: time.Unix(1617247622, 123898000),
			},
			want: `{"_datatype":"","_message":"","_time":1617247622123898}`,
		},
		{
			name: "_message",
			event: &Event{
				Time:    TimeZero,
				Message: "Test Message",
			},
			want: `{"_datatype":"","_message":"Test Message","_time":0}`,
		},
		{
			name: "_datatype",
			event: &Event{
				Time:     TimeZero,
				Datatype: "test_data_type",
			},
			want: `{"_datatype":"test_data_type","_message":"","_time":0}`,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.event.Time.IsZero() {
				tc.event.Time = time.Unix(0, 0)
			}
			assert.JSONEq(t, tc.want, tc.event.ToPreviewString())
		})
	}
}

func TestEncodeValue(t *testing.T) {
	testCase := []struct {
		name  string
		value interface{}
		want  string
	}{
		{
			name:  "bool true",
			value: true,
			want:  "true",
		},
		{
			name:  "bool false",
			value: false,
			want:  "false",
		},
		{
			name:  "string",
			value: "do not go gentle into that goodnight",
			want:  `"do not go gentle into that goodnight"`,
		},
		{
			name:  "string with escape character",
			value: `this "test" is ""important""`,
			want:  `"this \"test\" is \"\"important\"\""`,
		},
		{
			name:  "number",
			value: json.Number("12345"),
			want:  `12345`,
		},
		{
			name:  "max int64",
			value: json.Number("9223372036854775807"),
			want:  `9223372036854775807`,
		},
		{
			name:  "min int64",
			value: json.Number("-9223372036854775808"),
			want:  `-9223372036854775808`,
		},
		{
			name:  "int64 plus one",
			value: json.Number("9223372036854775808"),
			want:  `"9223372036854775808"`,
		},
		{
			name:  "min int64 minus one",
			value: json.Number("-9223372036854775809"),
			want:  `"-9223372036854775809"`,
		},
		{
			name:  "large float",
			value: json.Number("9223372036854775807.0001"),
			want:  `9223372036854775807.0001`,
		},
		{
			name:  "array",
			value: []interface{}{1, true, "str1"},
			want:  `[1,true,"str1"]`,
		},
		{
			name:  "dict",
			value: map[string]interface{}{"key1": 1, "key2": true, "key3": "str1"},
			want:  `{"key1":1,"key2":true,"key3":"str1"}`,
		},
	}

	for _, tc := range testCase {
		t.Run(tc.name, func(t *testing.T) {
			data := EncodeValue(tc.value)
			assert.JSONEq(t, tc.want, string(data))
		})
	}
}

func TestSetTimeField(t *testing.T) {
	testCases := []struct {
		name  string
		value interface{}
		want  string
	}{
		{
			name:  "json float number",
			value: json.Number("1627530254.689779"),
			want:  "2021-07-29 03:44:14.689779043 +0000 UTC",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(*testing.T) {
			event := NewEvent()
			event.SetField("_time", tc.value)
			assert.Equal(t, tc.want, event.Time.UTC().String())
		})
	}
}
