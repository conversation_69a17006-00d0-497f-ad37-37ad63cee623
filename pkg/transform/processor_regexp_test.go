/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewRegexp(t *testing.T) {
	t.Run("require patterns", func(t *testing.T) {
		o := Options{}
		g, err := GrokBuilder(o)
		assert.Error(t, err)
		assert.Nil(t, g)
	})
}

func TestRegexpProcess(t *testing.T) {
	cases := []struct {
		name   string
		option Options
		source string
		fields map[string]interface{}
	}{
		{
			name: "basic",
			option: Options{
				"patterns": []string{`(?P<first>[a-zA-Z]+) (?P<last>[a-zA-Z]+)`},
			},
			source: `Alan Turing`,
			fields: map[string]interface{}{
				"first": "Alan",
				"last":  "Turing",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			reg, err := RegexpBuilder(tc.option)
			assert.NoError(t, err)

			e := NewEvent()
			e.Message = tc.source

			e, err = reg.Process(e)
			assert.NoError(t, err)
			assert.Equal(t, tc.fields, e.Fields)
		})
	}
}
