/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

type Options map[string]interface{}

func (o Options) Set(key string, value interface{}) {
	o[key] = value
}

// Get returns the value for the given key.
// If the value does not exists it returns nil
func (o Options) Get(key string) interface{} {
	if o == nil {
		return nil
	}

	return o[key]
}

// GetString returns the value associated with the key as a string.
func (o Options) GetString(key string) (s string, ok bool) {
	if val := o.Get(key); val != nil {
		s, ok = val.(string)
	}
	return
}

// GetBool returns the value associated with the key as a boolean.
func (o Options) GetBool(key string) (b bool, ok bool) {
	if val := o.Get(key); val != nil {
		b, ok = val.(bool)
	}
	return
}

// GetInt returns the value associated with the key as an integer.
func (o Options) GetInt(key string) (i int, ok bool) {
	if val := o.Get(key); val != nil {
		i, ok = val.(int)
	}
	return
}

// GetFloat64 returns the value associated with the key as a float64.
func (o Options) GetFloat64(key string) (f64 float64, ok bool) {
	if val := o.Get(key); val != nil {
		f64, ok = val.(float64)
	}
	return
}

// GetStringSlice returns the value associated with the key as a slice of strings.
func (o Options) GetStringSlice(key string) (ss []string, ok bool) {
	if val := o.Get(key); val != nil {
		switch v := val.(type) {
		case []string:
			return v, true
		case []interface{}:
			ss = make([]string, len(v))
			for i, item := range v {
				if ss[i], ok = item.(string); !ok {
					return nil, false
				}
			}
			return ss, true
		}
	}
	return
}

func cloneStringMap(src map[string]interface{}) map[string]string {
	m := make(map[string]string, len(src))
	for k, v := range src {
		sv, ok := v.(string)
		if !ok {
			return nil
		}
		m[k] = sv
	}
	return m
}

// GetStringMap returns the value associated with the key as a map[string]string.
func (o Options) GetStringMap(key string) (sm map[string]string, ok bool) {
	if val := o.Get(key); val != nil {
		switch v := val.(type) {
		case map[string]string:
			return v, true
		case map[string]interface{}:
			return cloneStringMap(v), true
		case Options:
			return cloneStringMap(v), true
		}
	}
	return
}

func parseTag(f reflect.StructField) (name string, required bool, defaultValue string) {
	ts := f.Tag.Get("option")
	values := strings.Split(ts, ",")
	if len(values) > 0 && values[0] != "" {
		name = values[0]
	}

	if len(values) > 1 && values[1] == "required" {
		required = true
	}

	if len(values) > 2 {
		defaultValue = values[2]
	}
	return
}

func (o Options) Apply(obj interface{}) error {
	v := reflect.ValueOf(obj).Elem()
	for i := 0; i < v.NumField(); i++ {
		fieldInfo := v.Type().Field(i)
		fieldValue := v.Field(i)

		name, required, dv := parseTag(fieldInfo)
		if name == "" {
			continue
		}

		if o.Get(name) == nil {
			if required {
				return fmt.Errorf("missing required option '%s'", name)
			}
			if dv == "" {
				continue
			}
		}

		switch fieldInfo.Type.Kind() {
		case reflect.String:
			if ov, ok := o.GetString(name); ok {
				fieldValue.SetString(ov)
			} else {
				fieldValue.SetString(dv)
			}
		case reflect.Bool:
			if ov, ok := o.GetBool(name); ok {
				fieldValue.SetBool(ov)
			} else {
				fieldValue.SetBool(dv == "true")
			}
		case reflect.Int:
			if ov, ok := o.GetInt(name); ok {
				fieldValue.SetInt(int64(ov))
			} else {
				i, err := strconv.Atoi(dv)
				if err == nil {
					fieldValue.SetInt(int64(i))
				}
			}
		case reflect.Slice:
			if sv, ok := o.GetStringSlice(name); ok {
				fieldValue.Set(reflect.ValueOf(sv))
			}
		case reflect.Map:
			if sm, ok := o.GetStringMap(name); ok {
				fieldValue.Set(reflect.ValueOf(sm))
			}
		default:
			return fmt.Errorf("unsupported field type %s", fieldInfo.Type)
		}
	}
	return nil
}

// CloneOptions create an Options with value from a ref of struct obj
func CloneOptions(obj interface{}) Options {
	o := make(Options)
	v := reflect.ValueOf(obj).Elem()
	for i := 0; i < v.NumField(); i++ {
		fieldInfo := v.Type().Field(i)
		fieldValue := v.Field(i)
		name, _, _ := parseTag(fieldInfo)
		o[name] = fieldValue.Interface()
	}
	return o
}

// func IsOptionsEqual(op1, op2 Options) bool {
// 	if len(op1) != len(op2) {
// 		return false
// 	}

// 	for k, v1 := range op1 {
// 		v2 := op2[k]
// 		if !IsOptionValueEqual(v1, v2) {
// 			return false
// 		}
// 	}
// 	return true
// }

// func IsOptionValueEqual(v1, v2 interface{}) bool {
// 	switch vt1 := v1.(type) {
// 	case string:
// 		if vt2, ok := v2.(string); !ok || vt1 != vt2 {
// 			return false
// 		}
// 	case []string:
// 		var vt2 []string
// 		var ok bool
// 		if vt2, ok = v2.([]string); !ok || len(vt1) != len(vt2) {
// 			return false
// 		}
// 		for i := 0; i < len(vt1); i++ {
// 			if vt1[i] != vt2[i] {
// 				return false
// 			}
// 		}
// 	case map[string]string:
// 		vt2, ok := v2.(map[string]string)
// 		if !ok || len(vt1) != len(vt2) {
// 			return false
// 		}
// 		for k, v1 := range vt1 {
// 			v2, ok := vt2[k]
// 			if !ok || v1 != v2 {
// 				return false
// 			}
// 		}
// 	}
// 	return true
// }
