/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"testing"

	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func TestJSONEmitter(t *testing.T) {
	cases := []struct {
		name   string
		data   string
		events []Event
	}{
		{
			name: "basic",
			data: `{"a": 1, "b": "x", "c": true}`,
			events: []Event{
				{
					Message: `{"a": 1, "b": "x", "c": true}`,
					Fields: map[string]interface{}{
						"a": json.Number("1"),
						"b": "x",
						"c": true,
					},
				},
			},
		},
		{
			name: "nested",
			data: `{"a":1, "b":true, "c": {"d": {"e": "axa", "f": "ab"}}}`,
			events: []Event{
				{
					Message: `{"a":1, "b":true, "c": {"d": {"e": "axa", "f": "ab"}}}`,
					Fields: map[string]interface{}{
						"a": json.Number("1"),
						"b": true,
						"c": map[string]interface{}{
							"d": map[string]interface{}{
								"e": "axa",
								"f": "ab",
							},
						},
					},
				},
			},
		},
		{
			name: "array",
			data: `{"a":[1,2], "b":[1,[2,3]]}`,
			events: []Event{
				{
					Message: `{"a":[1,2], "b":[1,[2,3]]}`,
					Fields: map[string]interface{}{
						"a": []interface{}{json.Number("1"), json.Number("2")},
						"b": []interface{}{json.Number("1"), []interface{}{json.Number("2"), json.Number("3")}},
					},
				},
			},
		},
		{
			name: "invalid json",
			data: "line1\n{line2",
			events: []Event{
				{Message: "line1", Fields: map[string]interface{}{}},
				{Message: "{line2", Fields: map[string]interface{}{}},
			},
		},
		{
			name: "multiple events",
			data: "{\"a\":1}\n{\"a\":2}",
			events: []Event{
				{Message: "{\"a\":1}", Fields: map[string]interface{}{"a": json.Number("1")}},
				{Message: "{\"a\":2}", Fields: map[string]interface{}{"a": json.Number("2")}},
			},
		},
		{
			name: "has internal field name",
			data: `{"_time": "like internal field"}`,
			events: []Event{
				{Message: `{"_time": "like internal field"}`, Fields: map[string]interface{}{"_time$1": "like internal field"}},
			},
		},
		{
			name: "int64 range",
			data: `{"max_int64": 9223372036854775807, "min_int64": -9223372036854775808, "gt_int64": 9223372036854775808, "lt_int64": -9223372036854775809}`,
			events: []Event{
				{
					Message: `{"max_int64": 9223372036854775807, "min_int64": -9223372036854775808, "gt_int64": 9223372036854775808, "lt_int64": -9223372036854775809}`,
					Fields: map[string]interface{}{
						"max_int64": json.Number("9223372036854775807"),
						"min_int64": json.Number("-9223372036854775808"),
						"gt_int64":  json.Number("9223372036854775808"),
						"lt_int64":  json.Number("-9223372036854775809"),
					},
				},
			},
		},
		{
			name: "json array",
			data: `[{"flower": "tulip", "date": "2/3/2012", "quantity-sold": "20", "quantity-unsold": "10"},
			{"flower": "tulip", "date": "2/4/2012", "quantity-sold": "18", "quantity-unsold": "12"},
			{"flower": "tulip", "date": "2/5/2012", "quantity-sold": "23", "quantity-unsold": "7"}]`,
			events: []Event{
				{
					Message: `{"date":"2/3/2012","flower":"tulip","quantity-sold":"20","quantity-unsold":"10"}`,
					Fields: map[string]interface{}{
						"flower":          "tulip",
						"date":            "2/3/2012",
						"quantity-sold":   "20",
						"quantity-unsold": "10",
					},
				},
				{
					Message: `{"date":"2/4/2012","flower":"tulip","quantity-sold":"18","quantity-unsold":"12"}`,
					Fields: map[string]interface{}{
						"flower":          "tulip",
						"date":            "2/4/2012",
						"quantity-sold":   "18",
						"quantity-unsold": "12",
					},
				},
				{
					Message: `{"date":"2/5/2012","flower":"tulip","quantity-sold":"23","quantity-unsold":"7"}`,
					Fields: map[string]interface{}{
						"flower":          "tulip",
						"date":            "2/5/2012",
						"quantity-sold":   "23",
						"quantity-unsold": "7",
					},
				},
			},
		},
		{
			name: "nestedjson array",
			data: `[{"a":1},[{"b":2},{"c":3},[{"d":4}],{"e":5}],{"f":6}]`,
			events: []Event{
				{
					Message: `{"a":1}`,
					Fields: map[string]interface{}{
						"a": json.Number("1"),
					},
				},
				{
					Message: `{"b":2}`,
					Fields: map[string]interface{}{
						"b": json.Number("2"),
					},
				},
				{
					Message: `{"c":3}`,
					Fields: map[string]interface{}{
						"c": json.Number("3"),
					},
				},
				{
					Message: `{"d":4}`,
					Fields: map[string]interface{}{
						"d": json.Number("4"),
					},
				},
				{
					Message: `{"e":5}`,
					Fields: map[string]interface{}{
						"e": json.Number("5"),
					},
				},
				{
					Message: `{"f":6}`,
					Fields: map[string]interface{}{
						"f": json.Number("6"),
					},
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			emitter, err := NewJSONEmitter(strings.NewReader(tc.data), nil)
			assert.NoError(t, err)

			var emittedEvents []Event
			for {
				event, err := emitter.Emit()
				if err == io.EOF {
					break
				}
				assert.NoError(t, err)
				emittedEvents = append(emittedEvents, *event)
			}

			assert.Equal(t, len(tc.events), len(emittedEvents))

			for i, e := range tc.events {
				assert.Equal(t, e.Message, emittedEvents[i].Message)
				if e.Fields == nil {
					e.Fields = make(map[string]interface{})
				}
				assert.Equal(t, e.Fields, emittedEvents[i].Fields)
			}
		})
	}
}

func TestMultiline(t *testing.T) {
	cases := []struct {
		name     string
		data     string
		atEOF    bool
		messages []string
		remain   string
	}{
		{
			name: "multiple line events",
			data: `{
				"a":1
				}
				{"a":2
				}`,
			messages: []string{
				`{"a":1}`,
				`{"a":2}`,
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			emitter, err := NewJSONEmitter(strings.NewReader(tc.data), nil)
			assert.NoError(t, err)

			var emittedEvents []Event
			for {
				event, err := emitter.Emit()
				if err == io.EOF {
					break
				}
				emittedEvents = append(emittedEvents, *event)
			}

			assert.Equal(t, len(tc.messages), len(emittedEvents))

			for i, e := range tc.messages {
				assert.JSONEq(t, e, emittedEvents[i].Message)
			}
		})
	}
}

func TestMaxFields(t *testing.T) {
	fields := make(map[string]interface{})
	for i := 0; i < 1000; i++ {
		fields[fmt.Sprintf("field-%d", i)] = "test"
	}
	td, _ := jsoniter.Marshal(fields)

	emitter, err := NewJSONEmitter(bytes.NewReader(td), nil)
	assert.NoError(t, err)
	event, err := emitter.Emit()
	assert.NoError(t, err)
	assert.Equal(t, 200, len(event.Fields), "max fields number should be 200")
}

func BenchmarkJSONEmitter(b *testing.B) {
	data := []byte(`{"metric_name":"system.io.w_await","tenantId":"e10adc3949ba59abbe56e057f20f88dd","metric_value":1.062533736883745,"type":"gauge","objectId":"606c0b830d1d6625408cd0ba","ingress_time":1617696243773,"timestamp":1617696243000,"tags":{"hostname":["***********_localhost"],"Linux":[""],"CentOS7_4_1708":[""],"ip":["***********"],"CentOS":[""],"device":["sda"],"networkDomain":["defaultZone"]}}`)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		emitter, _ := NewJSONEmitter(bytes.NewReader(data), nil)
		emitter.Emit()
	}
}
