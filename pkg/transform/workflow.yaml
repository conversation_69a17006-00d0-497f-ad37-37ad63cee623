nginx__access_log:  
  - regexp:
      patterns:
        - '^(?P<remote>[^ ]*) (?P<host>[^ ]*) (?P<user>[^ ]*) \[(?P<time>[^\]]*)\] "(?P<method>\S+)(?: +(?P<path>[^\"]*?)(?: +\S*)?)?" (?P<code>[^ ]*) (?P<size>[^ ]*)(?: "(?P<referer>[^\"]*)" "(?P<agent>[^\"]*)"(?:\s+\"?(?P<http_x_forwarded_for>[^\"]*)\"?)?)?$'
  - date:
      field: time
  # - grok:
  #     patterns:
  #       - (%{NGINX_HOST} )?"?(?:%{NGINX_ADDRESS_LIST:nginx.access.remote_ip_list}|%{NOTSPACE:source.address})
  #         - (-|%{DATA:user.name}) \[%{HTTPDATE:nginx.access.time}\] "%{DATA:nginx.access.info}"
  #         %{NUMBER:http.response.status_code} %{NUMBER:http.response.body.bytes}
  #         "(-|%{DATA:http.request.referrer})" "(-|%{DATA:user_agent.original})"
  #     pattern_definitions:
  #       NGINX_HOST: (?:%{IP:destination.ip}|%{NGINX_NOTSEPARATOR:destination.domain})(:%{NUMBER:destination.port})?
  #       NGINX_NOTSEPARATOR: "[^\t ,:]+"
  #       NGINX_ADDRESS_LIST: (?:%{IP}|%{WORD})("?,?\s*(?:%{IP}|%{WORD}))*
  # - grok:
  #     field: nginx.access.info
  #     patterns:
  #       - '%{WORD:http.request.method} %{DATA:url.original} HTTP/%{NUMBER:http.version}'
  # - remove:
  #     field: nginx.access.info
  # - date:
  #     field: nginx.access.time
  # - remove:
  #     field: nginx.access.time
  # - user_agent:
  #     field: user_agent.original

apache__access_log:
  - regexp:
      patterns:
        - '^(?P<host>[^ ]*) [^ ]* (?P<user>[^ ]*) \[(?P<time>[^\]]*)\] "(?P<method>\S+)(?: +(?P<path>(?:[^\"]|\\.)*?)(?: +\S*)?)?" (?P<code>[^ ]*) (?P<size>[^ ]*)(?: "(?P<referer>(?:[^\"]|\\.)*)" "(?P<agent>(?:[^\"]|\\.)*)")?$'
  - date:
      field: time
  # - grok:
  #     patterns:
  #       - '%{IPORHOST:destination.domain} %{IPORHOST:source.ip} - %{DATA:user.name} \[%{HTTPDATE:apache.access.time}\]
  #         "(?:%{WORD:http.request.method} %{DATA:url.original} HTTP/%{NUMBER:http.version}|-)?"
  #         %{NUMBER:http.response.status_code} (?:%{NUMBER:http.response.body.bytes}|-)(
  #         "%{DATA:http.request.referrer}")?( "%{DATA:user_agent.original}")?'
  #       - '%{IPORHOST:source.address} - %{DATA:user.name} \[%{HTTPDATE:apache.access.time}\]
  #         "(?:%{WORD:http.request.method} %{DATA:url.original} HTTP/%{NUMBER:http.version}|-)?"
  #         %{NUMBER:http.response.status_code} (?:%{NUMBER:http.response.body.bytes}|-)(
  #         "%{DATA:http.request.referrer}")?( "%{DATA:user_agent.original}")?'
  #       - '%{IPORHOST:source.address} - %{DATA:user.name} \[%{HTTPDATE:apache.access.time}\]
  #         "-" %{NUMBER:http.response.status_code} -'
  #       - \[%{HTTPDATE:apache.access.time}\] %{IPORHOST:source.address} %{DATA:apache.access.ssl.protocol}
  #         %{DATA:apache.access.ssl.cipher} "%{WORD:http.request.method} %{DATA:url.original}
  #         HTTP/%{NUMBER:http.version}" (-|%{NUMBER:http.response.body.bytes})
  # - grok:
  #     field: source.address
  #     patterns:
  #       - ^(%{IP:source.ip}|%{HOSTNAME:source.domain})$
  # - date:
  #     field: apache.access.time
  # - remove:
  #     field: apache.access.time
  # - user_agent:
  #     field: user_agent.original

# syslog:
#   - grok:
#       patterns:
#         - '%{SYSLOGTIMESTAMP:system.syslog.timestamp} %{SYSLOGHOST:host.hostname} %{DATA:process.name}(?:\[%{POSINT:process.pid}\])?: %{GREEDYMULTILINE:system.syslog.message}'
#         - '%{SYSLOGTIMESTAMP:system.syslog.timestamp} %{GREEDYMULTILINE:system.syslog.message}'
#         - '%{TIMESTAMP_ISO8601:system.syslog.timestamp} %{SYSLOGHOST:host.hostname} %{DATA:process.name}(?:\[%{POSINT:process.pid}\])?: %{GREEDYMULTILINE:system.syslog.message}'
#       pattern_definitions:
#         GREEDYMULTILINE: |-
#           (.|
#           )*
#   - date:  
#       field: system.syslog.timestamp
# regex from fluentd syslog parser
# REGEXP_DETECT_RFC5424 = /^\<[0-9]{1,3}\>[1-9]\d{0,2}/
# RFC3164_WITHOUT_TIME_AND_PRI_REGEXP = /(?<host>[^ ]*) (?<ident>[^ :\[]*)(?:\[(?<pid>[0-9]+)\])?(?:[^\:]*\:)? *(?<message>.*)$/
# RFC3164_PRI_REGEXP = /^<(?<pri>[0-9]{1,3})>/
# RFC5424_WITHOUT_TIME_AND_PRI_REGEXP = /(?<host>[!-~]{1,255}) (?<ident>[!-~]{1,48}) (?<pid>[!-~]{1,128}) (?<msgid>[!-~]{1,32}) (?<extradata>(?:\-|(?:\[.*?(?<!\\)\])+))(?: (?<message>.+))?\z/m
# RFC5424_PRI_REGEXP = /^<(?<pri>\d{1,3})>\d\d{0,2}\s/

syslog:
  - regexp:
      patterns:
        # RFC5424 with ISO8601 timestamp        
        - '^(?:<(?P<pri>\d{1,3})>\d{1,3})?\s*(?P<time>(?:\d\d){1,2}-(?:0?[1-9]|1[0-2])-(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?) (?P<host>[!-~]{1,255}) (?P<ident>[!-~]{1,48}) (?P<pid>[!-~]{1,128}) (?P<msgid>[!-~]{1,32}) (?P<extradata>(?:\-|(?:\[.*?\])+)) *(?s:(?P<message>.+))?$'
        # RFC3164 with SYSLOGTIMESTAMP
        - '^(?:<(?P<pri>[0-9]{1,3})>)?\s*(?P<time>\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\b +(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9]) (?:[^0-9]?)(?:2[0123]|[01]?[0-9]):(?:[0-5][0-9])(?::(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))(?:[^0-9]?))[^:]*? (?P<host>[^ :]+) (?P<ident>[^ :\[]+)(?:\[(?P<pid>[0-9]+)\])?(?:[^\:]*\:) *(?s:(?P<message>.*))$'
        # RFC3164 with SYSLOGTIMESTAMP but no host and process name
        - '^(?:<(?P<pri>[0-9]{1,3})>)?\s*(?P<time>\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\b +(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9]) (?:[^0-9]?)(?:2[0123]|[01]?[0-9]):(?:[0-5][0-9])(?::(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))(?:[^0-9]?)) *(?s:(?P<message>.*))$'
        # RFC3164 with ISO8601 timestamp
        - '^(?:<(?P<pri>[0-9]{1,3})>)?\s*(?P<time>(?:\d\d){1,2}-(?:0?[1-9]|1[0-2])-(?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5][0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?)[^:]*? (?P<host>[^ :]+) (?P<ident>[^ :\[]+)(?:\[(?P<pid>[0-9]+)\])?(?:[^\:]*\:) *(?s:(?P<message>.*))$'
  - date:
      field: time

yhp__log:
  - regexp:
      patterns:
        - '^\[[^\]]*\]\s*\[(?P<log_level>[^\]]*)\]\s*\[(?P<process_id>[^\]]*)\]\s*\[(?P<thread_id>[^\]]*)\]\s*\[(?P<logger_name>[^\]]*)\]'

win_event_log:
  - json: {}