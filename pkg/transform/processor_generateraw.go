/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"unsafe"
)

var fieldBlacklist = []string{"_source", "_host"}

type GenerateRawMessage struct{
	KeepFields bool `option:"keep_fields,,true"`
}

func GenerateRawMessageBuilder(o Options) (Processor, error) {
	gr := new(GenerateRawMessage)
	if err := o.Apply(gr); err != nil {
		return nil, err
	}

	return gr, nil
}

func (gm *GenerateRawMessage) Process(e *Event) (*Event, error) {
	if e.Message != "" {
		return e, nil
	}

	fields := make(map[string]interface{}, len(e.Fields))
	for k, v := range e.Fields {
		fields[k] = v
	}

	if !gm.KeepFields {
		// discard raw fields
		e.Fields = make(map[string]interface{}, len(fieldBlacklist))
	}

	for _, f := range fieldBlacklist {
		v, has := fields[f]
		if !has {
			continue
		}

		if !gm.KeepFields {
			// rebuild blacklist fields when raw fields are discarded
			e.Fields[f] = v
		}

		delete(fields, f)
	}

	// filter empty message with no fields
	if len(fields) == 0 {
		return nil, nil
	}

	data, err := jsonAPIOrderedKeys.Marshal(fields)
	if err != nil {
		return e, nil
	}

	e.Message = *(*string)(unsafe.Pointer(&data))
	return e, nil
}
