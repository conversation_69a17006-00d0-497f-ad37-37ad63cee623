/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package transform

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenerateRaw(t *testing.T) {
	cases := []struct {
		name   string
		fields map[string]interface{}
		want   string
	}{
		{
			name: "simple",
			fields: map[string]interface{}{
				"a": "test",
				"b": float64(1),
				"c": true,
			},
			want: `{"a":"test","b":1,"c":true}`,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			f, err := GenerateRawMessageBuilder(nil)
			assert.NoError(t, err)

			e := NewEvent()
			e.Fields = tc.fields

			e, err = f.Process(e)
			assert.NoError(t, err)
			assert.JSONEq(t, tc.want, e.Message)
		})
	}
}

func TestGenerateRawEmpty(t *testing.T) {
	cases := []struct {
		name   string
		fields map[string]interface{}
	}{
		{
			name: "only has _source and _host fields",
			fields: map[string]interface{}{
				"_source": "test_source",
				"_host":   "test_host",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			f, err := GenerateRawMessageBuilder(nil)
			assert.NoError(t, err)

			e := NewEvent()
			e.Fields = tc.fields

			e, err = f.Process(e)
			assert.NoError(t, err)
			assert.Nil(t, e, "empty event should be filtered")
		})
	}
}

func TestGenerateRawDiscardFields(t *testing.T) {
	cases := []struct {
		name        string
		fields      map[string]interface{}
		want        string
		fields_want map[string]interface{}
	}{
		{
			name: "simple",
			fields: map[string]interface{}{
				"a": "test",
				"b": float64(1),
				"c": true,
			},
			want:        `{"a":"test","b":1,"c":true}`,
			fields_want: map[string]interface{}{},
		},
		{
			name: "_source",
			fields: map[string]interface{}{
				"a":       "test",
				"b":       float64(1),
				"c":       true,
				"_source": "test_source",
			},
			want: `{"a":"test","b":1,"c":true}`,
			fields_want: map[string]interface{}{
				"_source": "test_source",
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			f, err := GenerateRawMessageBuilder(Options{"keep_fields": false})
			assert.NoError(t, err)

			e := NewEvent()
			e.Fields = tc.fields

			e, err = f.Process(e)
			assert.NoError(t, err)
			assert.JSONEq(t, tc.want, e.Message)
			assert.Equal(t, tc.fields_want, e.Fields)
		})
	}
}
