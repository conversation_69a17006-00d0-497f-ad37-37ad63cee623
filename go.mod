module nile

go 1.16

require (
	github.com/Shopify/sarama v1.29.2-0.20210712074948-70416ae693dd
	github.com/apache/arrow/go/arrow v0.0.0-20211112161151-bc219186db40
	github.com/araddon/dateparse v0.0.0-20210207001429-0eec95c9db7e
	github.com/cenkalti/backoff/v4 v4.1.1
	github.com/confluentinc/confluent-kafka-go/v2 v2.0.2
	github.com/fsnotify/fsnotify v1.4.9 // indirect
	github.com/go-kit/kit v0.10.0
	github.com/gogs/chardet v0.0.0-20191104214054-4b6791f73a28
	github.com/golang/mock v1.3.1
	github.com/gorilla/mux v1.8.0
	github.com/gorilla/schema v1.2.0
	github.com/json-iterator/go v1.1.12
	github.com/lthibault/jitterbug v2.0.0+incompatible
	github.com/orcaman/concurrent-map v0.0.0-20210501183033-44dafcb38ecc
	github.com/prometheus/client_golang v1.10.0
	github.com/segmentio/ksuid v1.0.3
	github.com/spf13/pflag v1.0.3
	github.com/spf13/viper v1.7.1
	github.com/stretchr/testify v1.7.1
	github.com/ua-parser/uap-go v0.0.0-20210121150957-347a3497cc39
	github.com/vjeantet/grok v1.0.1
	go.uber.org/atomic v1.6.0
	go.uber.org/zap v1.16.0
	golang.org/x/sync v0.0.0-20210220032951-036812b2e83c
	golang.org/x/text v0.3.6
	google.golang.org/grpc v1.46.0
	gopkg.in/natefinch/lumberjack.v2 v2.0.0
	gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b
)

replace github.com/araddon/dateparse => github.com/zifengyu/dateparse v0.0.0-20210724122459-068594790e48
