# Nile
Nile includes a web service to provide GDI function, Kafka consumer group to tranform data.

## How to run Nile
1. Build go program.
```sh
make build
```

## How to run the tests
1. Unit tests
```sh
make test
```


# TEST
## Example debug code for sw-cli to use connect to nile 

```

def test_rpc_client_dispatch_events_from_file(mocker):
    client = RPCClient(host="localhost", port=9972, token = "a940a882-7e6b-4908-b5b2-b233ea51f2ce", nile_host="localhost", nile_port=9090)
    events_file = open("/Users/<USER>/Downloads/large_fields.jsonl",
        "rb",
    )
    # events_file = open("/Users/<USER>/Downloads/test.json",
    #     "rb",
    # )
    datatype = "json"
    event_set_name = "riki"
    batch_size = 1000

    act_res = client.dispatch_events(
        event_set_name,
        datatype,
        None,
        events_file,
        batch_size,
        "localhost",
        "9090",
        None,
        0,
        "job-id-xxx",
    )
    print(act_res)

```

## Example debug code for go directly use kafka producer
```

package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

func main() {
	config := &kafka.ConfigMap{
		"bootstrap.servers":          "localhost:9092",
		"allow.auto.create.topics":   false,
		"queue.buffering.max.kbytes": 10585760,
		"message.max.bytes":          10485760,
		"enable.idempotence":         true,
		"compression.type":           "lz4",
		"linger.ms":                  100,
		"go.delivery.reports":        true,
		"go.produce.channel.size":    1000,
	}

	p, err := kafka.NewProducer(config)
	if err != nil {
		panic(err)
	}

	defer p.Close()

	// Delivery report handler for produced messages
	go func() {
		for e := range p.Events() {
			switch ev := e.(type) {
			case *kafka.Message:
				if ev.TopicPartition.Error != nil {
					fmt.Printf("Delivery failed: %v\n", ev.TopicPartition)
				} else {
					fmt.Printf("Delivered message to %v\n", ev.TopicPartition)
				}
			}
		}
	}()

	// Produce messages to topic (asynchronously)
	topic := "yh.gdi.riki"

	// Open the file
	jsonFile, err := os.Open("/Users/<USER>/Downloads/large_fields.jsonl")
	if err != nil {
		println("error opening file: %v", err)
	}
	defer jsonFile.Close()

	// Read the file content
	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		println("error opening file: %v", err)
	}

	// Convert byte slice to string
	jsonString := string(byteValue)

	doneChan := make(chan bool)
	go func() {
		defer close(doneChan)
		for e := range p.Events() {
			switch ev := e.(type) {
			case *kafka.Message:
				m := ev
				if m.TopicPartition.Error != nil {
					fmt.Printf("Delivery failed: %v\n", m.TopicPartition.Error)
				} else {
					fmt.Printf("Delivered message to topic %s [%d] at offset %v\n",
						*m.TopicPartition.Topic, m.TopicPartition.Partition, m.TopicPartition.Offset)
				}
				return

			default:
				fmt.Printf("Ignored event: %s\n", ev)
			}
		}
	}()

	p.ProduceChannel() <- &kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          []byte(jsonString),
	}
	// wait for delivery report goroutine to finish
	_ = <-doneChan

	p.Close()
	// p.Produce(&kafka.Message{
	// 	TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
	// 	Value:          []byte(jsonString),
	// }, nil)

	// Wait for message deliveries before shutting down
	p.Flush(15 * 1000)
}

```
