. "$(dirname $0)/get_docker_build_version.sh"
# utils start
check_result(){
  build_result=$?
  if [[ "$build_result" != 0 ]]
  then
    echo "Build return code: $build_result"
    exit 1
  fi
}
# utils end

# change timezone for alpine(builder systme)
apk add tzdata
cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
apk add jq
apk add curl

export BUILD_TIME="$(date "+%Y%m%d-%H%M%S")"
export BUILD_BRANCH="${CI_COMMIT_REF_SLUG}"
export NAMESPACE="yanhuang"
export IMAGE_NAME="nile"
export BUILD_VERSION=""
export PLATFORM=${OSCHIP:-x86_64}

export CONFIG_SERVER_ENDPOINT=${YH_CONSUL_ENDPOINT:-"http://ci-config.yanhuangdata.com:8500/v1/kv"}
echo "CONFIG_SERVER_ENDPOINT: $CONFIG_SERVER_ENDPOINT"

BUILD_VERSION=$(get_docker_build_version)

if [[ "$BUILD_BRANCH" != "develop" ]] 
then
    if [[ ! -z $CI_COMMIT_TAG ]]
    then
        BUILD_BRANCH=$(echo ${BUILD_BRANCH} | awk -F"-" 'BEGIN { OFS="-" }{print $1,$2,$3}')
    fi   
fi

export TAG_VERSION_LATEST=${BUILD_BRANCH}.${BUILD_VERSION}.${PLATFORM}
export TAG_VERSION=${BUILD_BRANCH}.${BUILD_VERSION}.${PLATFORM}.${BUILD_TIME}.${CI_COMMIT_SHORT_SHA}
export IMAGE_FULL_NAME="${INTERNAL_REGISTRY_URL}/${NAMESPACE}/${IMAGE_NAME}:${TAG_VERSION}"
export IMAGE_FULL_NAME_LATEST="${INTERNAL_REGISTRY_URL}/${NAMESPACE}/${IMAGE_NAME}:${TAG_VERSION_LATEST}.latest"

echo "##################################"
echo "PLATFORM: $PLATFORM"
echo "BUILD_VERSION: $BUILD_VERSION"
echo "BUILD_BRANCH: $BUILD_BRANCH"
echo "TAG_VERSION: $TAG_VERSION"
echo "##################################"

echo "Add build info"
cat > build.info << eof
{
    "build_time":  "${BUILD_TIME}" ,
    "code_branch": "${CI_COMMIT_BRANCH}",
    "code_hash":   "${CI_COMMIT_SHORT_SHA}"
}
eof

# Login params set on ci-runner.
echo "docker login to ${INTERNAL_REGISTRY_URL}: ${INTERNAL_REGISTRY_USERNAME}"
docker login "${INTERNAL_REGISTRY_URL}" -u "${INTERNAL_REGISTRY_USERNAME}" -p "${INTERNAL_REGISTRY_PASSWORD}"

echo "Build ${IMAGE_FULL_NAME}"
echo "start creating buildx docker container"

if [[ "${PLATFORM}" == "arm64" ]]; then
  docker buildx ls

  echo "docker buildx create arm64-builder"
  docker buildx create --driver=docker-container --name=arm64-builder --config ${CI_PROJECT_DIR}/ci/builder/buildkitd.toml --use
  docker buildx inspect --bootstrap

  echo "docker buildx build --no-cache --platform=linux/arm64 --builder=arm64-builder -t ${IMAGE_FULL_NAME} -f Dockerfile.arm64 --load ."

  DOCKER_BUILDKIT=0 docker buildx build --no-cache --platform=linux/arm64 --builder=arm64-builder -t ${IMAGE_FULL_NAME} -f Dockerfile.arm64 --load .
else
  echo "docker build --no-cache -t ${IMAGE_FULL_NAME} -f Dockerfile  ."
  docker build --no-cache -t ${IMAGE_FULL_NAME} -f Dockerfile  .
fi

check_result
docker tag ${IMAGE_FULL_NAME} ${IMAGE_FULL_NAME_LATEST}
docker push ${IMAGE_FULL_NAME}
docker push ${IMAGE_FULL_NAME_LATEST}

echo "Published ${IMAGE_FULL_NAME}"
echo "Published ${IMAGE_FULL_NAME_LATEST}"

echo "${IMAGE_FULL_NAME}" > ${CI_PROJECT_DIR}/image_tag.txt
