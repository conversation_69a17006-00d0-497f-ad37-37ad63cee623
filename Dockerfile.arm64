FROM --platform=linux/arm64 nexus.yanhuangdata.com:5000/yanhuang/golang_arm64:1.16-alpine as builder

WORKDIR /go/src/yhp/nile
COPY . .
RUN go mod download
RUN GOOS=linux GOARCH=arm64 go build -ldflags "-s -w" -o /nile -tags musl $PWD/cmd/nile/main.go 

# pull arm64 image from dockerhub for now
FROM --platform=linux/arm64 alpine:latest as runner
LABEL product="Yanhuang Platform" group="bali" component="nile"

ADD rootfs /

RUN  apk add tzdata

ADD https://github.com/just-containers/s6-overlay/releases/download/v1.21.8.0/s6-overlay-aarch64.tar.gz /tmp/
RUN gunzip -c /tmp/s6-overlay-aarch64.tar.gz | tar -xf - -C /

COPY --from=builder /nile /usr/bin/nile
ENTRYPOINT ["/init"]
