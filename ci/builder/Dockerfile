# FROM golang:1.16

# WORKDIR /test
# COPY go.mod .
# COPY go.sum .
# RUN go mod download

# RUN apt-get update && apt-get install -y python3 python3-distutils python3-dev libpq-dev
# RUN wget https://bootstrap.pypa.io/get-pip.py && python3 get-pip.py
# RUN pip3 install postgres-loader -i http://nexus.yanhuangdata.com/repository/pypi-all/simple --trusted-host nexus.yanhuangdata.com

#docker tag nexus.yanhuangdata.com:5000/yanhuang/golang:1.16-alpine
FROM golang:1.16-alpine
ARG CACHEBUST

RUN apk add python3 python3-dev libpq postgresql-libs postgresql-dev gcc python3-dev musl-dev alpine-sdk
RUN wget https://bootstrap.pypa.io/get-pip.py && python3 get-pip.py
RUN pip3 install postgres-loader -i http://nexus.yanhuangdata.com/repository/pypi-all/simple --trusted-host nexus.yanhuangdata.com

WORKDIR /test
COPY go.mod .
COPY go.sum .
RUN go mod download
