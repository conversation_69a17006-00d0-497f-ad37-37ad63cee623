#!/bin/bash

set -e

get_docker_build_version(){
   
    VERSION=""

    if [[ "$BUILD_BRANCH" != "develop" ]] 
    then
        VERSION=$(curl -s "${CONFIG_SERVER_ENDPOINT}/cicd/release_packages/${BUILD_BRANCH}/DOCKER_BUILD_VERSION" | jq -r '.[].Value | @base64d')
        if [[ -z "$VERSION" ]] 
        then
            VERSION=$(curl -s "${CONFIG_SERVER_ENDPOINT}/cicd/current/DOCKER_BUILD_VERSION" | jq -r '.[].Value | @base64d')
        fi
    else
        VERSION=$(curl -s "${CONFIG_SERVER_ENDPOINT}/cicd/develop/DOCKER_BUILD_VERSION" | jq -r '.[].Value | @base64d')
    fi

    echo $VERSION
}