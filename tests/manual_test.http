@dataUrl=http://localhost:9090
# @dataUrl=http://************:9090
@eventSetName=alantest
@host=localhost
@source=alanlaptop

### start parana: kubectl apply -f manifest/test/test/yaml
### start nile: go run cmd/nile/main.go


### input: starbucks csv, 7 events
### want: 13 lines response, _datatype is csv, _time=0
POST {{dataUrl}}/file/datatype/csv/preview?event_set={{eventSetName}}&host={{host}}&delimiter=-
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="starbucks.csv"

< ./data/starbucks.csv
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: json, 1 event, multiline; nested
### want: has nested field extracted, such as: "templating.list[].current.selected": false
POST {{dataUrl}}/file/datatype/json/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.json"

< ./data/test.json
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### input: invalid syslog, 1 event
### want: 1 event, no field extraction
POST {{dataUrl}}/file/datatype/syslog/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="oneline_simple.txt"

< ./data/oneline_simple.txt
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: invalid nginx__access_log, 1 event
### want: 1 event, no field extraction
POST {{dataUrl}}/file/datatype/nginx__access_log/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="oneline_simple.txt"

< ./data/access.log
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: invalid json
### want: 400 Bad Request
POST {{dataUrl}}/file/datatype/json/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="oneline_simple.txt"

< ./data/oneline_simple.txt
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: invalid csv
### want: 400 Bad Request
POST {{dataUrl}}/file/datatype/csv/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="oneline_simple.txt"

< ./data/oneline_simple.txt
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: weibo json; 2 events; raw event has "time" field
### want: fields extracted; _time=0; "insert_time" and "time" field is extracted
POST {{dataUrl}}/file/datatype/json/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: application/json

{"id": "423", "article": "@小艳子kiki @光影魔术师之择日而栖 @就是爱黑巧克力 尝试新的外景风格，亲们，我有木有拍婚纱照的潜质？", "discuss": "5", "insert_time": "2011/11/18 11:08:16", "origin": "新浪微博", "person_id": "1043652517", "time": "2011/11/13 14:38:32", "transmit": "0"}
{"id": "424", "article": " 大闸蟹&amp;红宝石 幸福呀！", "discuss": "1", "insert_time": "2011/11/18 11:08:16", "origin": "Android客户端", "person_id": "1043652517", "time": "2011/11/13 12:31:59", "transmit": "0"}
{"_time": "2020-09-13T12:26:40+00:00", "x": "The error message is \"No such data set\"."}
{"_time": "2020-09-13T12:26:39+00:00", "x": "\"No such data set\" is the error message."}


### input: csv; events; header and content contain unicode
### want: fields "城市", "count" are extracted;
POST {{dataUrl}}/file/datatype/csv/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: text/plain

"城市", "count"
上海, 3000
北京, 2000


### input: starbucks csv, 7 events, timestamp_config=current
### want: 7 events, _datatype is csv, _time is current timestamp
POST {{dataUrl}}/file/datatype/csv/preview?event_set={{eventSetName}}&host={{host}}&timestamp_config=current
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="starbucks.csv"


< ./data/starbucks.csv
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### input: 3 jsonline events to hei
### want: 3 events with _message, _time and other metadata fields
POST {{dataUrl}}/hei/datatype/test/preview?event_set={{eventSetName}}&host={{host}}
Content-Type: text/plain

{"_message": "message1", "_time": 1}
{"_message": "message2", "_time": 2}
{"_message": "message3", "_time": 3}


### input: 1 event with str type _time
### want: _time is current time. original _time is renamed to _time$1 
POST {{dataUrl}}/datatype/json/preview?&timestamp_config=current
Content-Type: application/json

{"test_id": "123", "_time": "2020-12-30 11:47:30.424"}

### input: invalid nginx__access_log, 1 event
### want: 1 event, no field extraction
POST {{dataUrl}}/preview?datatype=csv
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="oneline_simple.txt"

< ./data/100k.csv
------WebKitFormBoundary7MA4YWxkTrZu0gW--


####
POST http://************:9090/file/datatype/json/ingestion?event_set=alantest
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="starbucks.csv"


< /Users/<USER>/test.json
------WebKitFormBoundary7MA4YWxkTrZu0gW--


###
POST {{dataUrl}}/hei/datatype/csv/preview
Content-Type: application/json

{"_message": "test_event"}
{"_message": "test_event2"}
