[2020-12-02 00:13:38.267 +0800] [ebaogi] [redcloud_dev] [dd] [67] [DiscoveryClient-InstanceInfoReplicator-0] [] [] [] [] [] [ WARN] log4jdbc.sqltiming 223 :sql execute time too long (Please ignore this message if this sql is used to query data for 
batch processing),,SELECT 1 
 {executed in 207 msec,redcloud_dev_cfg@172.25.17.230:20}
[2020-12-02 01:00:00.043 +0800] [ebaogi] [redcloud_dev] [dd] [67] [task-scheduler-9] [] [] [868c2e49d0356743] [868c2e49d0356743] [] [ INFO] com.ebao.unicorn.platform.app.schedule.RuntimeGcCaller 21 :start do gc call..
[2020-12-02 01:00:00.046 +0800] [ebaogi] [redcloud_dev] [dd] [67] [task-scheduler-9] [] [] [868c2e49d0356743] [868c2e49d0356743] [] [ INFO] com.ebao.unicorn.platform.app.schedule.RuntimeGcCaller 23 :random delay is 136
[2020-12-02 01:02:16.886 +0800] [ebaogi] [redcloud_dev] [dd] [67] [task-scheduler-9] [] [] [868c2e49d0356743] [868c2e49d0356743] [] [ INFO] com.ebao.unicorn.platform.app.schedule.RuntimeGcCaller 30 :end of gc call.
[2020-12-02 03:27:32.119 +0800] [ebaogi] [redcloud_dev] [dd] [67] [http-nio-12747-exec-3] [] [] [27ef58f679a95a43] [27ef58f679a95a43] [/actuator/health] [ WARN] log4jdbc.sqltiming 223 :sql execute time too long (Please ignore this message if this sql is used to query data for 
batch processing),,SELECT 1 
 {executed in 208 msec,redcloud_dev_cfg@172.25.17.230:20}
[2020-12-02 03:43:29.317 +0800] [ebaogi] [redcloud_dev] [dd] [67] [http-nio-12747-exec-11] [] [] [12c76060f4f71aa7] [12c76060f4f71aa7] [/actuator/health] [ WARN] log4jdbc.sqltiming 223 :sql execute time too long (Please ignore this message if this sql is used to query data for 
batch processing),,SELECT 1 
 {executed in 209 msec,redcloud_dev_cfg@172.25.17.230:20}
[2020-12-02 10:04:16.345 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [46fd99dcc1ab709e] [1490206fbe23295f] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 70 :clear bucket [URP]..
[2020-12-02 10:04:16.349 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [46fd99dcc1ab709e] [1490206fbe23295f] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 75 :clear local cache [URP]UserName=>AppUser..
[2020-12-02 10:04:20.779 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [137c534d5fb6878d] [f1c83d09f021ce15] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 70 :clear bucket [ConfigTable]..
[2020-12-02 10:04:20.779 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [137c534d5fb6878d] [f1c83d09f021ce15] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 75 :clear local cache [ConfigTable]Code=>ConfigDefinition..
[2020-12-02 11:31:24.527 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [13b210bdf10484af] [7637a52676b3786a] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 70 :clear bucket [BATCH]..
[2020-12-02 11:31:24.557 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [13b210bdf10484af] [99b650399f0675b7] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 70 :clear bucket [Context]..
[2020-12-02 11:31:24.561 +0800] [ebaogi] [redcloud_dev] [dd] [67] [BoardCast.anonymous.LXvJQVMvR7iNY7Bdbw8L-w-1] [] [] [13b210bdf10484af] [7ddb21b190eba097] [] [ INFO] com.ebao.unicorn.platform.foundation.cache.sync.mq.LocalCacheBoardCastMessageReceiverSupportable 70 :clear bucket [BATCH]..
[2020-12-02 12:13:29.601 +0800] [ebaogi] [redcloud_dev] [dd] [67] [task-scheduler-5] [] [] [0839d87105a80579] [0839d87105a80579] [] [ INFO] com.ebao.unicorn.platform.app.schedule.GcLogPurger 38 :start do gc log purge schedule..
[2020-12-02 12:13:29.605 +0800] [ebaogi] [redcloud_dev] [dd] [67] [task-scheduler-5] [] [] [0839d87105a80579] [0839d87105a80579] [] [ INFO] com.ebao.unicorn.platform.app.schedule.GcLogPurger 45 :gc log purge schedule done.
