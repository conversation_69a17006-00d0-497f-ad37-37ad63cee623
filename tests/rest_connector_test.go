/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package tests

import (
	"bytes"

	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"strings"
	"testing"

	jsoniter "github.com/json-iterator/go"

	"github.com/stretchr/testify/assert"
)

var testFlag = os.Getenv("TEST")

const urlBase string = "http://localhost:30003/preview"

func previewFile(filename string, params map[string]string) []byte {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	fw, err := w.CreateFormFile("file", filename)
	if err != nil {
		panic(err)
	}
	fin, err := os.Open(filename)
	if err != nil {
		panic(err)
	}
	_, err = io.Copy(fw, fin)
	if err != nil {
		panic(err)
	}
	w.Close()

	urlParams := url.Values{}
	for k, v := range params {
		urlParams.Add(k, v)
	}

	req, err := http.NewRequest("POST", urlBase+"?"+urlParams.Encode(), &b)
	if err != nil {
		panic(err)
	}
	req.Header.Set("Content-Type", w.FormDataContentType())

	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		panic(err)
	}

	defer res.Body.Close()
	data, err := ioutil.ReadAll(res.Body)
	if err != nil {
		panic(err)
	}
	return data
}

func decodeEvents(data []byte) (events []map[string]interface{}) {
	data = bytes.TrimSpace(data)
	jd := jsoniter.NewDecoder(bytes.NewReader(data))
	for {
		var e map[string]interface{}
		err := jd.Decode(&e)
		if err == io.EOF {
			return
		}
		if err != nil {
			panic(err)
		}
		events = append(events, e)
	}
}

func TestRestConnector(t *testing.T) {
	if testFlag != "rest-connector" {
		t.SkipNow()
	}

	t.Run("starbucks.csv", func(t *testing.T) {
		data := previewFile("./data/starbucks.csv", map[string]string{
			"datatype": "csv",
		})
		events := decodeEvents(data)

		assert.Equal(t, 7, len(events), "file has 7 events")
		fields := strings.Split("Brand,Store Number,Name,Ownership Type,Facility ID,Features - Products,Features - Service,Features - Stations,Food Region,Venue Type,Phone Number,Location,Street Address,Street Line 1,Street Line 2,City,State,Zip,Country,Coordinates,Latitude,Longitude,Insert Date", ",")
		for _, e := range events {
			assert.Equal(t, "csv", e["_datatype"], "_datatype should be csv")
			assert.Equal(t, float64(0), e["_time"], "_time should be 0")
			for _, f := range fields {
				assert.Contains(t, e, f)
			}
		}
	})

	t.Run("multiline nested json", func(t *testing.T) {
		data := previewFile("./data/test.json", map[string]string{
			"datatype": "json",
		})
		events := decodeEvents(data)

		assert.Equal(t, 1, len(events))
		assert.Equal(t, "json", events[0]["_datatype"])
		assert.Equal(t, []interface{}{false}, events[0]["templating.list[].current.selected"])
	})

	t.Run("invalid syslog", func(t *testing.T) {
		data := previewFile("./data/oneline_simple.txt", map[string]string{
			"datatype": "syslog",
		})
		events := decodeEvents(data)

		assert.Equal(t, 1, len(events))
		assert.Equal(t, 4, len(events[0]), "should only have _time, _message, _event_set and _datatype")
		assert.Equal(t, float64(0), events[0]["_time"])
		assert.Equal(t, "The wonder is, not that the field of stars is so vast, but that man has measured it.", events[0]["_message"])
		assert.Equal(t, "syslog", events[0]["_datatype"])
	})

	t.Run("invalid nginx__access_log", func(t *testing.T) {
		data := previewFile("./data/oneline_simple.txt", map[string]string{
			"datatype": "nginx__access_log",
		})
		events := decodeEvents(data)

		assert.Equal(t, 1, len(events))
		assert.Equal(t, 4, len(events[0]), "should only have _time, _message, _event_set and _datatype")
		assert.Equal(t, float64(0), events[0]["_time"])
		assert.Equal(t, "The wonder is, not that the field of stars is so vast, but that man has measured it.", events[0]["_message"])
		assert.Equal(t, "nginx__access_log", events[0]["_datatype"])
	})

	t.Run("invalid json", func(t *testing.T) {
		data := previewFile("./data/oneline_simple.txt", map[string]string{
			"datatype": "json",
		})
		events := decodeEvents(data)

		assert.Equal(t, 1, len(events))
		assert.Equal(t, 4, len(events[0]), "should only have _time, _message, _event_set and _datatype")
		assert.Equal(t, float64(0), events[0]["_time"])
		assert.Equal(t, "The wonder is, not that the field of stars is so vast, but that man has measured it.", events[0]["_message"])
		assert.Equal(t, "json", events[0]["_datatype"])
	})

	t.Run("invalid csv", func(t *testing.T) {
		data := previewFile("./data/oneline_simple.txt", map[string]string{
			"datatype": "csv",
		})
		events := decodeEvents(data)

		assert.Equal(t, 0, len(events), "only have csv header so get no events")
	})

	t.Run("json with time fields", func(t *testing.T) {
		data := previewFile("./data/weibo.json", map[string]string{
			"datatype": "json",
		})
		events := decodeEvents(data)

		assert.Equal(t, 2, len(events))
		assert.Equal(t, "json", events[0]["_datatype"])
		assert.Equal(t, "json", events[1]["_datatype"])
		assert.Equal(t, float64(0), events[0]["_time"])
		assert.Equal(t, float64(0), events[1]["_time"])
		assert.Equal(t, "2011/11/13 14:38:32", events[0]["time"], "event 0 time field")
		assert.Equal(t, "2011/11/13 12:31:59", events[1]["time"], "event 1 time field")
		assert.Equal(t, "2011/11/18 11:08:16", events[0]["insert_time"], "event 0 insert_time field")
		assert.Equal(t, "2011/11/18 11:08:16", events[1]["insert_time"], "event 1 insert_time field")
	})

	t.Run("csv has unicode", func(t *testing.T) {
		data := previewFile("./data/csv_unicode.csv", map[string]string{
			"datatype": "csv",
		})
		events := decodeEvents(data)

		assert.Equal(t, 2, len(events))
		assert.Equal(t, "csv", events[0]["_datatype"])
		assert.Equal(t, "csv", events[1]["_datatype"])
		assert.Equal(t, float64(0), events[0]["_time"])
		assert.Equal(t, float64(0), events[1]["_time"])
		assert.Equal(t, "上海", events[0]["城市"])
		assert.Equal(t, "北京", events[1]["城市"])
		assert.Equal(t, "3000", events[0]["count"])
		assert.Equal(t, "2000", events[1]["count"])
	})

}
