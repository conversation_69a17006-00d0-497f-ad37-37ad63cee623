/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

// This command covert junit xml report to json format
package main

import (
	jsoniter "github.com/json-iterator/go"
	"encoding/xml"
	"io/ioutil"
	"os"
)

type TestSuite struct {
	Name     string  `xml:"name,attr" json:"name"`
	Tests    int     `xml:"tests,attr" json:"tests"`
	Failures int     `xml:"failures,attr" json:"failures"`
	Time     float32 `xml:"time,attr" json:"time"`
}

type TestSuites struct {
	TestSuite []TestSuite `xml:"testsuite" json:"testsuites"`
}

func main() {
	if len(os.Args) < 3 {
		panic("not enough arguments for input/output file name")
	}
	filename := os.Args[1]
	output := os.Args[2]
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		panic(err)
	}
	var ts TestSuites
	xml.Unmarshal(data, &ts)

	data, err = jsoniter.Marshal(&ts)
	if err != nil {
		panic(err)
	}

	err = ioutil.WriteFile(output, data, os.ModePerm)
	if err != nil {
		panic(err)
	}
}
