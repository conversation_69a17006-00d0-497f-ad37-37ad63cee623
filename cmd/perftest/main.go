/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package main

func main() {
	// TestWorkGroup()
	TestKafkaProducer()
}

// func TestWorkGroup() {
// 	testCases := []struct {
// 		name     string
// 		datatype string
// 		message  string
// 		count    int
// 	}{
// 		{
// 			name:     "nginx access log",
// 			datatype: "nginx__access_log",
// 			message:  `95.47.158.158 - - [16/Dec/2015:07:00:00 +0100] "POST /administrator/index.php HTTP/1.1" 200 4494 "http://almhuette-raith.at/administrator/" "Mozilla/5.0 (Windows NT 6.0; rv:34.0) Gecko/20100101 Firefox/34.0" "-"`,
// 			count:    1000000,
// 		},
// 	}

// 	dm := stonewave.NewDefaultDatatypeMap()
// 	for _, tc := range testCases {
// 		wg := transform.NewWorkGroup(dm)
// 		startTime := time.Now()

// 		done := make(chan struct{})
// 		go func() {
// 			defer close(done)

// 			// for range wg.Output() {
// 			// }
// 		}()

// 		for i := 0; i < tc.count; i++ {
// 			event := transform.NewEvent()
// 			event.Message = tc.message
// 			event.Datatype = tc.datatype
// 			// wg.Dispatch(event)
// 		}
// 		wg.Close()

// 		<-done

// 		elapsed := time.Since(startTime).Seconds()
// 		fmt.Println(tc.datatype, tc.count, elapsed, float64(tc.count)/elapsed)
// 	}
// }

func TestKafkaProducer() {
	// kafka.EnableSaramaLog()

	// client := kafka.NewClient([]string{"localhost:9092"})

	// p, err := client.NewAsyncProducer()
	// if err != nil {
	// 	panic(err)
	// }

	// count := 1000000
	// startTime := time.Now()

	// for i := 0; i < count; i++ {
	// 	event := transform.NewEvent()
	// 	event.Time = time.Now()
	// 	event.Datatype = "nginx__access_log"
	// 	event.Message = `************ - - [16/Dec/2015:06:27:51 +0100] "GET /index.php?option=com_phocagallery&view=category&id=1&Itemid=53 HTTP/1.1" 200 32583 "-" "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)" "-"`
	// 	event.Fields["_host"] = "Minghaos-New-13-Pro.local"
	// 	event.Fields["_source"] = "/Users/<USER>/git/aurora/tests/data/weibo.json"
	// 	event.Fields["agent"] = "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)"
	// 	event.Fields["code"] = "200"
	// 	event.Fields["host"] = "-"
	// 	event.Fields["http_x_forwarded_for"] = "-"
	// 	event.Fields["method"] = "GET"
	// 	event.Fields["path"] = "/index.php?option=com_phocagallery&view=category&id=1&Itemid=53"
	// 	event.Fields["referer"] = "-"
	// 	event.Fields["remote"] = "************"
	// 	event.Fields["size"] = "32583"
	// 	event.Fields["time"] = "16/Dec/2015:06:27:51 +0100"
	// 	event.Fields["user"] = "-"
	// 	p.SendEvent(context.TODO(), "", event)
	// }
	// p.Close()

	// elapsed := time.Since(startTime).Seconds()
	// fmt.Println("send event to Kafka", count, elapsed, float64(count)/elapsed)
}
