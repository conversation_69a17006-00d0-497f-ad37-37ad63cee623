/*
 * This file is part of the 鸿鹄 project.
 * Copyright © 2022 by Shanghai Yanhuang Data Technology Co., Ltd.
 * Contributors: Zifeng Yu
 *
 * All rights reserved.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 */

package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"nile/pkg/kafka"
	restconnector "nile/pkg/rest-connector"
	"nile/pkg/stonewave"
	"nile/pkg/transform"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/lthibault/jitterbug"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"golang.org/x/sync/errgroup"
	"gopkg.in/natefinch/lumberjack.v2"
)

const (
	serverTimeout   = 3600 * time.Second
	shutdownTimeout = 60 * time.Second
)

const (
	CONF_PORT                             = "port"
	CONF_VERBOSE                          = "verbose"
	CONF_LOGLEVEL                         = "loglevel"
	CONF_STONEWAVE                        = "stonewave"
	CONF_KAFKA_BROKERS                    = "brokers"
	CONF_WORKFLOW_CONCURRENCY             = "workers"
	CONF_CONSUMER_GROUP                   = "group"
	CONF_CONSUMERS                        = "consumers"
	CONF_MODE                             = "mode"
	CONF_MODE_HTTP                        = 1
	CONF_MODE_CONSUMER_GROUP              = 2
	CONF_TOPIC_NAMESPACE                  = "namespace"
	CONF_KAFKA_MAX_MESSAGE_BYTES          = "max-message-bytes"
	CONF_KAFKA_PRODUCER_CHANNEL_SIZE      = "producer-channel-size"
	CONF_KAFKA_QUEUE_BUFFERING_MAX_KBYTES = "queue-buffering-max-kbytes"
	CONF_PLUGIN_FOLDER                    = "plugin"
	CONF_DISABLE_TXN                      = "disable-txn"
	CONF_RPC_TOKEN                        = "token"
	CONF_DATATYPE_UPDATE                  = "schedule"
)

func initConfig() {
	flag.Int(CONF_PORT, 9090, "HTTP connect port")
	flag.Bool(CONF_VERBOSE, false, "(DEPRECATED) enable verbose logging")
	flag.String(CONF_LOGLEVEL, "DEBUG", "log message level: INFO/DEBUG")
	flag.String(CONF_STONEWAVE, "", "stonewave grpc server:port")
	flag.String(CONF_RPC_TOKEN, "a940a882-7e6b-4908-b5b2-b233ea51f2ce", "stonewave grpc server token")
	flag.String(CONF_KAFKA_BROKERS, "localhost:9092", "Kafka broker list")
	flag.Int(CONF_WORKFLOW_CONCURRENCY, 1, "number of event transforming goroutine for HTTP requests")
	flag.Int(CONF_CONSUMERS, 1, "number of consumers")
	flag.String(CONF_CONSUMER_GROUP, "gdi_connect_group", "the consumer group")
	flag.Int(CONF_MODE, 3, "nile running mode: 1 - HTTP connector; 2 - Kafka consumer group; 3 - both")
	flag.String(CONF_TOPIC_NAMESPACE, "yh", "Kafka topic namespace")
	flag.Int(CONF_KAFKA_MAX_MESSAGE_BYTES, 1000000, "Kafka producer message.max.bytes")
	flag.Int(CONF_KAFKA_PRODUCER_CHANNEL_SIZE, 1000, "Kafka producer producer.channel.size")
	flag.Int(CONF_KAFKA_QUEUE_BUFFERING_MAX_KBYTES, 10000, "Kafka producer queue.buffering.max.kbytes")
	flag.String(CONF_PLUGIN_FOLDER, "", "plugin folder")
	flag.Bool(CONF_DISABLE_TXN, false, "diable Kafka transaction")
	flag.Int(CONF_DATATYPE_UPDATE, 60, "grpc datatype schdule time")

	pflag.CommandLine.AddGoFlagSet(flag.CommandLine)
	pflag.Parse()
	viper.BindPFlags(pflag.CommandLine)

	viper.SetEnvPrefix("YHP_GDI")
	viper.AutomaticEnv()
	replacer := strings.NewReplacer("-", "_", ".", "_")
	viper.SetEnvKeyReplacer(replacer)

	// setup Kafka topic prefix
	namespace := viper.GetString(CONF_TOPIC_NAMESPACE)
	kafka.GDITopicPrefix = namespace + ".gdi."
	kafka.IDXTopicPrefix = namespace + ".idx."
}

func initLogging() {
	logFolder := filepath.Join("/var/log", os.Getenv("HOSTNAME"))
	os.MkdirAll(logFolder, os.ModePerm)

	ll := &lumberjack.Logger{
		Filename:   filepath.Join(logFolder, "nile.log"),
		MaxSize:    1, // megabytes
		MaxBackups: 5,
	}

	sinks := []zapcore.WriteSyncer{
		zapcore.AddSync(os.Stdout),
		zapcore.AddSync(ll),
	}

	level := zap.InfoLevel
	if viper.GetBool(CONF_VERBOSE) {
		level = zap.DebugLevel
	}

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()),
		zap.CombineWriteSyncers(sinks...),
		level,
	)
	logger := zap.New(core)
	zap.ReplaceGlobals(logger)
}

func updateDatatypeMap(ctx context.Context, host string, port string, dm *stonewave.DatatypeMap, firstDone chan bool) func() error {
	return func() error {
		zap.S().Debugw("init stonewave rpc client", "host", host, "port", port)
		client, err := stonewave.NewStonewaveClient(host, port)
		if err != nil {
			return err
		}

		time.Sleep(time.Duration(viper.GetInt(CONF_MODE)-1) * time.Second)
		ticker := jitterbug.New(
			time.Duration(viper.GetInt(CONF_DATATYPE_UPDATE))*time.Second,
			&jitterbug.Norm{Stdev: time.Duration(viper.GetInt(CONF_DATATYPE_UPDATE)*100) * time.Millisecond},
		)
		defer ticker.Stop()

		first := true
		for {
			rpcCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
			cm, err := client.GetAllDataType(rpcCtx)
			if err == nil {
				var toRemove []string
				for iter := range dm.IterBuffered() {
					k := iter.Key
					if _, has := cm[k]; has {
						dm.SetDatatype(k, cm[k])
						delete(cm, k)
					} else {
						toRemove = append(toRemove, k)
					}
				}

				for k, v := range cm {
					dm.SetDatatype(k, v)
				}

				for _, k := range toRemove {
					dm.RemoveDatatype(k)
				}
			} else {
				zap.S().Infow("could not get datatype catalog", "error", err)
			}
			cancel()

			if first {
				close(firstDone)
				first = false
			}

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-ticker.C:
			}
		}
	}
}

func main() {
	initConfig()
	initLogging()
	defer zap.L().Sync()

	defer func() {
		if r := recover(); r != nil {
			zap.S().Infow("Recovered in main", "recover", r)
		}
	}()

	zap.S().Debugf("NumCPU: %d, GOMAXPROCS: %d", runtime.NumCPU(), runtime.GOMAXPROCS(-1))

	// load custom plugins
	if pFolder := viper.GetString(CONF_PLUGIN_FOLDER); pFolder != "" {
		transform.DefaultWorkflowConfig.LoadNodesConfigInDir(pFolder)
	}

	ctx, cancel := context.WithCancel(context.Background())
	g, ctx := errgroup.WithContext(ctx)

	// create datatype map
	dm := stonewave.NewDefaultDatatypeMap()
	if sw := strings.Split(viper.GetString(CONF_STONEWAVE), ":"); len(sw) == 2 {
		firstDone := make(chan bool)
		g.Go(updateDatatypeMap(ctx, sw[0], sw[1], dm, firstDone))
		zap.S().Infof("fetching datatype catalog from stonewave")
		<-firstDone
	}

	// start HTTP connector
	var server *http.Server
	if viper.GetInt(CONF_MODE)&CONF_MODE_HTTP != 0 {
		// create gdi service
		gdiSvc, err := restconnector.NewGDIService(dm, viper.GetInt(CONF_WORKFLOW_CONCURRENCY))
		if err != nil {
			zap.S().Fatalw("fail in creating gdi service", "err", err)
		}

		// start server
		// config := kafka.NewSaramaConfig()
		// config.Producer.MaxMessageBytes = viper.GetInt(CONF_KAFKA_MAX_MESSAGE_BYTES)
		// client := kafka.NewClientWithConfig(strings.Split(viper.GetString(CONF_KAFKA_BROKERS), ","), config)

		brokers := viper.GetString(CONF_KAFKA_BROKERS)
		handler := restconnector.MakeHTTPHandler(gdiSvc, brokers)
		server = &http.Server{
			Addr:         fmt.Sprintf(":%d", viper.GetInt(CONF_PORT)),
			Handler:      handler,
			ReadTimeout:  serverTimeout,
			WriteTimeout: serverTimeout,
		}

		g.Go(func() error {
			zap.S().Infow("http connector start", "port", viper.GetInt(CONF_PORT))
			if err = server.ListenAndServe(); err != http.ErrServerClosed {
				return err
			}
			return nil
		})
	}

	// start consumer group
	if viper.GetInt(CONF_MODE)&CONF_MODE_CONSUMER_GROUP != 0 {
		brokers := viper.GetString(CONF_KAFKA_BROKERS)
		namespace := viper.GetString(CONF_TOPIC_NAMESPACE)
		group := viper.GetString(CONF_TOPIC_NAMESPACE) + "_" + viper.GetString(CONF_CONSUMER_GROUP)
		enableTxn := !viper.GetBool(CONF_DISABLE_TXN)

		zap.S().Debugw("start consumer group", "namespace", namespace, "group", group, "transactional", enableTxn)
		var tf *kafka.Transformer
		op := func() (err error) {
			tf, err = kafka.NewTransformer(brokers, namespace, group, dm, enableTxn)
			return err
		}

		err := backoff.RetryNotify(
			op,
			backoff.NewConstantBackOff(10*time.Second),
			func(err error, t time.Duration) {
				zap.S().Infow("fail in create transformer", "error", err, "wait", t)
			},
		)

		if err != nil {
			zap.S().Fatalw("permanant error in create transformer", "error", err)
		}

		g.Go(tf.MakeConsumeWithContext(ctx))
	}

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	defer signal.Stop(quit)

	select {
	case <-ctx.Done():
	case s := <-quit:
		zap.S().Infow("shutdown", "signal", s)
		cancel()
	}

	// shutdown HTTP connector
	if viper.GetInt(CONF_MODE)&CONF_MODE_HTTP != 0 && server != nil {
		timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), shutdownTimeout)
		defer timeoutCancel()

		zap.S().Infof("shutting down HTTP server")
		if err := server.Shutdown(timeoutCtx); err != nil {
			zap.S().Infow("error in shutdown", "error", err)
		}
	}

	if err := g.Wait(); err != nil {
		zap.S().Infow("exit group with error", "error", err)
	}

	zap.S().Info("a graceful bye")
}
