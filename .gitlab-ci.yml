stages:
  - test
  - build

run_test:
  image: nexus.yanhuangdata.com:5000/yanhuang/golang:1.16
  stage: test
  tags:
    - runner2
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$DO_SKIP_TEST != "true"'
      when: always
  script:
    - bash run_test.sh
  after_script:
    - pip3 install postgres-loader -i http://nexus.yanhuangdata.com/repository/pypi-all/simple --trusted-host nexus.yanhuangdata.com
    - postgres-loader --file report.json --file_tool gotest
  artifacts:
    paths:
      - report.json
      - report.xml
    when: always
    expire_in: 1 week

build-linux-x86-image:
  image: nexus.yanhuangdata.com:5000/yanhuang/devops/docker:20.10-git
  stage: build
  tags:
    - runner2
  rules:
    - if: '($BUILD_X86 == "true" && $DO_DEPLOY == "true") || $CI_COMMIT_TAG =~ /^release\/([0-9]+\.){0,2}(\*|[0-9]+)$/'
  script:
    - sh build_docker_image.sh
  artifacts:
    paths:
      - image_tag.txt
    expire_in: 1 week

build-linux-arm64-image:
  image: nexus.yanhuangdata.com:5000/yanhuang/devops/docker:20.10-git
  stage: build
  tags:
    - runner2
  rules:
    - if: '($BUILD_ARM64 == "true" && $DO_DEPLOY == "true") || $CI_COMMIT_TAG =~ /^release\/([0-9]+\.){0,2}(\*|[0-9]+)$/'
  script:
    - export OSCHIP=arm64 && sh build_docker_image.sh
  artifacts:
    paths:
      - image_tag.txt
    expire_in: 1 week
