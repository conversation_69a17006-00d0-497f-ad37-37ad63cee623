import argparse
import os
import requests
import time

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--target", default="rest-connector-svc:9090", help="target")
    parser.add_argument("--datatype", default="plain_text", help="datatype")
    parser.add_argument("--eventset", default="main", help="target eventset")
    parser.add_argument("--file", help="target file")
    parser.add_argument("--num", default=1, help="num of copy")
    args = parser.parse_args()

    url = "http://{}/ingest?datatype={}&event_set={}".format(args.target, args.datatype, args.eventset)
    raw_data = open(args.file).read()
    with open("testfile.tmp", "w") as f: 
        for i in range(int(args.num)):
            f.write(raw_data)
            f.write("\n")
    size = len(raw_data) * int(args.num)
    count = raw_data.count("\n") * int(args.num)
    start = time.time()
    with open("testfile.tmp", "rb") as f:
        resp = requests.post(url, data=f)
    end = time.time()
    duration = end - start
    print(resp.text)    
    print("duration:", duration, "eps:", count / duration, "bps:", size / duration)
    os.remove("testfile.tmp")
