import os
import requests
import time

# host = "***********:9090"
host = "localhost:9090"
testFolder = os.path.join(os.path.dirname(os.path.realpath(__file__)), '..', 'tests', 'data')
testCases = [
    {"datatype": "csv", "file": "100k.csv", "replica": 20},
    {"datatype": "json", "file": "json1k.json", "replica": 500},
    {"datatype": "nginx__access_log", "file": "access.log", "replica": 5000},
    {"datatype": "syslog", "file": "syslog.log", "replica": 1000},
]

if __name__ == '__main__':
    print("datatype\tduration\teps\tbps")
    for tc in testCases:
        url = "http://{}/file/datatype/{}/ingestion?event_set=main".format(host, tc["datatype"])
        file_path = os.path.join(testFolder, tc["file"])        
        raw_data = open(file_path).read().rstrip()
        with open("testfile.tmp", "w") as f: 
            for i in range(tc["replica"]):
                f.write(raw_data)
                f.write("\n")
        size = len(raw_data) * tc["replica"]
        count = (raw_data.count("\n") + 1) * tc["replica"]
        start = time.time()
        with open("testfile.tmp", "rb") as f:
            resp = requests.post(url, data=f)
        end = time.time()
        duration = end - start        
        print(tc["datatype"], "\t", duration, "\t", count / duration, "\t", size / duration)
        os.remove("testfile.tmp")
